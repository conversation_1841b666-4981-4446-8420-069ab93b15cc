<?php
require_once('webset.php');

// التحقق من وجود الرقم المرجعي
if (!isset($_GET['ref'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_ref = $_GET['ref'];

// جلب بيانات الحجز
$stmt = $db->prepare("SELECT b.*, t.* FROM bus_bookings b
                     JOIN bus_trips t ON b.trip_id = t.id
                     WHERE b.booking_reference = ?");
$stmt->execute([$booking_ref]);
$booking = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$booking) {
    header('Location: bus-booking.php');
    exit;
}

// تحويل التاريخ إلى العربية
$date = new DateTime($booking['travel_date']);
$day = $date->format('d');
$month = $date->format('n');
$year = $date->format('Y');
$day_name = $date->format('w');

$arabic_days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
$arabic_months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$arabic_date = $arabic_days[$day_name] . '، ' . $day . ' ' . $arabic_months[$month] . ' ' . $year;

// إعداد الـ PDF
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تذكرة الباص - <?php echo $booking['booking_reference']; ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .ticket {
            background: white;
            max-width: 600px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .ticket-header {
            background: #ff9500;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .ticket-body {
            padding: 30px;
        }
        
        .route-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .station {
            text-align: center;
            flex: 1;
        }
        
        .station-from {
            color: #28a745;
        }
        
        .station-to {
            color: #ff9500;
        }
        
        .arrow {
            font-size: 24px;
            color: #666;
            margin: 0 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        
        .booking-ref {
            background: #333;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 2px;
        }
        
        .qr-section {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .ticket {
                box-shadow: none;
                max-width: none;
            }
        }
        
        @media (max-width: 600px) {
            .route-section {
                flex-direction: column;
                gap: 15px;
            }
            
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="ticket">
        <!-- رأس التذكرة -->
        <div class="ticket-header">
            <h1 style="margin: 0; font-size: 24px;">تذكرة الباص</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">شركة النقل السياحي</p>
        </div>
        
        <!-- رقم الحجز -->
        <div class="booking-ref">
            <?php echo $booking['booking_reference']; ?>
        </div>
        
        <!-- محتوى التذكرة -->
        <div class="ticket-body">
            <!-- المسار -->
            <div class="route-section">
                <div class="station station-from">
                    <div style="font-size: 12px; color: #28a745; margin-bottom: 5px;">سفر من</div>
                    <div style="font-weight: bold; font-size: 18px;"><?php echo $booking['from_city']; ?></div>
                    <div style="font-size: 14px; color: #666;"><?php echo $booking['from_station']; ?></div>
                </div>
                
                <div class="arrow">←</div>
                
                <div class="station station-to">
                    <div style="font-size: 12px; color: #ff9500; margin-bottom: 5px;">وصول إلى</div>
                    <div style="font-weight: bold; font-size: 18px;"><?php echo $booking['to_city']; ?></div>
                    <div style="font-size: 14px; color: #666;"><?php echo $booking['to_station']; ?></div>
                </div>
            </div>
            
            <!-- معلومات الرحلة -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">تاريخ السفر</div>
                    <div class="info-value"><?php echo $arabic_date; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">وقت المغادرة</div>
                    <div class="info-value"><?php echo $booking['departure_time']; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">اسم المسافر</div>
                    <div class="info-value"><?php echo $booking['passenger_name']; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">رقم الهاتف</div>
                    <div class="info-value"><?php echo $booking['passenger_phone']; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">عدد المسافرين</div>
                    <div class="info-value"><?php echo $booking['seats_count']; ?> مسافر</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">المبلغ المدفوع</div>
                    <div class="info-value"><?php echo $booking['total_amount']; ?> ريال</div>
                </div>
            </div>
            
            <!-- رمز الاستجابة السريعة -->
            <div class="qr-section">
                <div style="font-size: 14px; color: #666; margin-bottom: 10px;">رمز التحقق</div>
                <div style="font-family: monospace; font-size: 16px; font-weight: bold; color: #333;">
                    <?php echo strtoupper(substr($booking['booking_reference'], -8)); ?>
                </div>
            </div>
            
            <!-- تعليمات -->
            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin-top: 20px; border-radius: 4px;">
                <div style="font-weight: bold; color: #856404; margin-bottom: 10px;">تعليمات مهمة:</div>
                <ul style="margin: 0; padding-right: 20px; color: #856404; font-size: 14px;">
                    <li>يرجى الوصول قبل 30 دقيقة من موعد المغادرة</li>
                    <li>أحضر هويتك الشخصية أو جواز السفر</li>
                    <li>احتفظ بهذه التذكرة حتى نهاية الرحلة</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
