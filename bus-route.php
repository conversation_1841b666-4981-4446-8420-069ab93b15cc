<?php
// التحقق من وجود معامل الرابط
if (!isset($_GET['route']) || empty($_GET['route'])) {
    header('Location: bus-booking.php');
    exit;
}

$route_link = $_GET['route'];

include_once('webset.php');

// جلب بيانات الصفحة المخصصة
try {
    $stmt = $db->prepare("SELECT * FROM bus_cities WHERE link = ? AND status = 1");
    $stmt->execute([$route_link]);
    $route_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$route_data) {
        header('Location: bus-booking.php');
        exit;
    }
} catch (Exception $e) {
    header('Location: bus-booking.php');
    exit;
}

// تعيين متغيرات الصفحة
$Title_page = $route_data['title'];
$meta_description = $route_data['meta_description'] ?: $route_data['descr'];
$meta_keywords = $route_data['meta_keywords'] ?: $route_data['tags'];

include_once('header.php');
include_once('navbar.php');

// تحديد نوع الرحلة والمدن
$trip_type = $route_data['trip_type'];
$from_city = $route_data['from_city'];
$to_city = $route_data['to_city'];
$from_station = $route_data['from_station'];
$to_station = $route_data['to_station'];

// تحديد النصوص حسب نوع الرحلة
$trip_type_text = $trip_type == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة';
$tab_active_class = $trip_type == 'to_mecca' ? 'to_mecca' : 'from_mecca';
$tab_inactive_class = $trip_type == 'to_mecca' ? 'from_mecca' : 'to_mecca';

echo '
<section class="bus-booking-hero" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); min-height: 100vh; position: relative; overflow: hidden;">
    <div class="container-fluid h-100">
        <div class="row align-items-center h-100">
            <!-- النص والعنوان مع صورة الباص -->
            <div class="col-lg-7 hero-text-section">
                <div class="hero-content text-white">
                    <div class="hero-badge mb-3">
                        <span class="badge-text" style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 14px;">' . $trip_type_text . '</span>
                    </div>
                    <h1 class="hero-title" style="font-size: 3.5rem; font-weight: bold; line-height: 1.2; margin-bottom: 20px;">' . htmlspecialchars($route_data['title_s']) . '<br><span style="color: #fff3e0;">يومياً</span></h1>
                    
                    <!-- وصف الصفحة -->
                    <div class="route-description" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin: 20px 0;">
                        <p style="font-size: 18px; line-height: 1.6; margin: 0;">' . htmlspecialchars($route_data['descr']) . '</p>
                    </div>
                    
                    <div class="bus-image-container" style="position: relative; margin-top: 40px;">
                        <img src="'.$Site_URL.'/img/bus-image-ar.png" alt="Bus" class="bus-image" style="width: 100%; height: auto; border-radius: 15px;">
                    </div>
                </div>
            </div>

            <!-- نموذج الحجز المخصص -->
            <div class="col-lg-5 booking-section">
                <div class="booking-form-container" style="padding: 0px;">
                    <div class="booking-form-card" style="background: white; border-radius: 20px; padding: 30px; box-shadow: 0 20px 60px rgba(0,0,0,0.1);">
                        <!-- عنوان النموذج -->
                        <div class="form-header mb-4">
                            <h3 style="color: #333; margin-bottom: 15px; text-align: center;">' . htmlspecialchars($route_data['title_s']) . '</h3>
                            
                            <!-- عرض المسار المحدد مسبقاً -->
                            <div class="preset-route" style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                                <div class="route-display" style="display: flex; align-items: center; justify-content: center;">
                                    <div class="city-badge" style="background: #28a745; color: white; padding: 8px 15px; border-radius: 20px; font-weight: bold;">
                                        ' . htmlspecialchars($from_city) . '
                                    </div>
                                    <div style="margin: 0 15px; color: #ff9500; font-size: 24px;">
                                        <i class="fa fa-arrow-left"></i>
                                    </div>
                                    <div class="city-badge" style="background: #ff9500; color: white; padding: 8px 15px; border-radius: 20px; font-weight: bold;">
                                        ' . htmlspecialchars($to_city) . '
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form id="busSearchForm">
                            <input type="hidden" id="tripType" name="trip_type" value="' . $trip_type . '">
                            <input type="hidden" id="presetFromCity" value="' . htmlspecialchars($from_city) . '">
                            <input type="hidden" id="presetToCity" value="' . htmlspecialchars($to_city) . '">
                            <input type="hidden" id="presetFromStation" value="' . htmlspecialchars($from_station ?: '') . '">
                            <input type="hidden" id="presetToStation" value="' . htmlspecialchars($to_station ?: '') . '">

                            <!-- المدينة والمحطة (مخفية أو للعرض فقط) -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        المدينة
                                    </label>
                                    <input type="text" class="form-control" value="' . htmlspecialchars($from_city) . '" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px; background: #f8f9fa;" readonly>
                                    <input type="hidden" id="fromCity" name="from_city" value="' . htmlspecialchars($from_city) . '">
                                </div>
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        المحطة
                                    </label>
                                    <select class="form-select" id="fromStation" name="from_station" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px;">
                                        <option value="">اختر المحطة</option>
                                    </select>
                                </div>
                            </div>

                            <!-- تاريخ السفر وعدد المسافرين -->
                            <div class="row mb-4">
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        تاريخ السفر
                                        <span class="text-warning">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="travelDate" name="travel_date" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px;" min="'.date('Y-m-d').'">
                                </div>
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        عدد المسافرين
                                    </label>
                                    <div class="passenger-counter" style="display: flex; align-items: center; border: 2px solid #e9ecef; border-radius: 10px; padding: 8px;">
                                        <button type="button" class="btn-counter" id="decreaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">-</button>
                                        <input type="number" class="form-control text-center" id="passengerCount" name="passenger_count" value="1" min="1" max="10" style="border: none; font-size: 18px; font-weight: bold; background: transparent;" readonly>
                                        <button type="button" class="btn-counter" id="increaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">+</button>
                                    </div>
                                    <small class="text-muted" style="font-size: 12px;">يُنصح بحجز مقعد للأطفال مع ولي الأمر</small>
                                </div>
                            </div>

                            <!-- زر البحث -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-search w-100" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 12px; padding: 15px; font-size: 18px; font-weight: bold; transition: all 0.3s;">
                                        <span class="search-icon">🔍</span>
                                        أعرض الرحلات المتاحة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم عرض النتائج -->
<section id="searchResults" class="search-results-section" style="display: none; padding: 60px 0; background: #f8f9fa;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center mb-5" style="color: #333; font-weight: bold;">الرحلات المتاحة</h2>
                <div id="tripsContainer" class="trips-container">
                    <!-- سيتم عرض الرحلات هنا -->
                </div>
            </div>
        </div>
    </div>
</section>';

// تضمين نفس CSS و JavaScript من الصفحة الأصلية
include('bus-booking-assets.php');

include('footer.php');
?>
