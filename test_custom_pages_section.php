<?php
include_once('webset.php');

echo "<h2>اختبار قسم الصفحات المخصصة</h2>";

// جلب الصفحات المخصصة النشطة
$custom_pages = [];
try {
    $stmt = $db->prepare("SELECT cp.page_name, cp.page_url, cp.page_title, bt.from_city, bt.to_city 
                         FROM custom_bus_pages cp 
                         JOIN bus_trips bt ON cp.trip_id = bt.id 
                         WHERE cp.is_active = 1 
                         ORDER BY cp.created_at DESC");
    $stmt->execute();
    $custom_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}

echo "<h3>عدد الصفحات المخصصة النشطة: " . count($custom_pages) . "</h3>";

if (!empty($custom_pages)) {
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">';
    echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">';
    
    echo '
    <style>
    .custom-page-item:hover {
        border-color: #ff9500;
        box-shadow: 0 4px 12px rgba(255, 149, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .custom-page-item .btn:hover {
        background: #ff7b00 !important;
        transform: scale(1.05);
    }
    
    .custom-page-item i {
        transition: all 0.3s ease;
    }
    
    .custom-page-item:hover i.fa-bus {
        transform: scale(1.1);
    }
    
    .custom-page-item:hover i.fa-arrow-left {
        transform: translateX(-3px);
    }
    </style>
    ';
    
    echo '
    <!-- قسم الصفحات المخصصة -->
    <section class="custom-pages-section" style="padding: 40px 0; background: #ffffff;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h3 style="color: #333; font-weight: bold; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-route" style="color: #ff9500; margin-left: 10px;"></i>
                        رحلات مخصصة متاحة
                    </h3>
                    <div class="custom-pages-list" style="max-width: 800px; margin: 0 auto;">
    ';
    
    foreach ($custom_pages as $page) {
        $page_url = $Site_URL . '/custom-bus-page.php?page=' . urlencode($page['page_url']);
        echo '
                        <div class="custom-page-item" style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; transition: all 0.3s ease;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h5 style="margin: 0 0 5px 0; color: #333; font-weight: 600;">
                                        <i class="fas fa-bus" style="color: #28a745; margin-left: 8px; font-size: 14px;"></i>
                                        ' . htmlspecialchars($page['page_name']) . '
                                    </h5>
                                    <p style="margin: 0; color: #666; font-size: 14px;">
                                        <span style="color: #28a745; font-weight: 500;">' . htmlspecialchars($page['from_city']) . '</span>
                                        <i class="fas fa-arrow-left" style="margin: 0 8px; color: #ff9500; font-size: 12px;"></i>
                                        <span style="color: #ff9500; font-weight: 500;">' . htmlspecialchars($page['to_city']) . '</span>
                                    </p>
                                </div>
                                <div>
                                    <a href="' . $page_url . '" class="btn" style="background: #ff9500; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; text-decoration: none; transition: all 0.3s ease;">
                                        <i class="fas fa-external-link-alt" style="margin-left: 5px; font-size: 12px;"></i>
                                        عرض الرحلة
                                    </a>
                                </div>
                            </div>
                        </div>
        ';
    }
    
    echo '
                    </div>
                </div>
            </div>
        </div>
    </section>
    ';
    
    echo "<h3>✅ تم عرض الصفحات المخصصة بنجاح!</h3>";
    echo "<p><strong>الروابط:</strong></p>";
    echo "<ul>";
    foreach ($custom_pages as $page) {
        $page_url = $Site_URL . '/custom-bus-page.php?page=' . urlencode($page['page_url']);
        echo "<li><a href='{$page_url}' target='_blank'>{$page['page_name']}</a></li>";
    }
    echo "</ul>";
    
} else {
    echo "<p>❌ لا توجد صفحات مخصصة نشطة</p>";
}
?>