# نظام الصفحات المخصصة للباصات (النسخة المحدثة)

## نظرة عامة
نظام مبسط لإنشاء صفحات مخصصة لرحلات محددة من جدول `bus_trips` الموجود، بدون الحاجة لصفحات إدارة.

## المميزات الجديدة
- ✅ **مربوط مباشرة بجدول bus_trips** - لا حاجة لإدخال البيانات يدوياً
- ✅ **بدون صفحات إدارة** - نظام مبسط
- ✅ **رحلات محددة مسبقاً** - كل صفحة تعرض رحلة واحدة فقط
- ✅ **بيانات حقيقية** - الأسعار والمحطات والمواعيد من قاعدة البيانات

## بنية الجدول الجديد
جدول `custom_bus_pages` يحتوي على:
- `id` - معرف الصفحة
- `trip_id` - معرف الرحلة من جدول bus_trips (Foreign Key)
- `page_name` - اسم الصفحة
- `page_title` - عنوان الصفحة
- `page_url` - رابط الصفحة
- `seo_title` - عنوان السيو
- `seo_description` - وصف السيو
- `page_content` - محتوى إضافي
- `is_active` - حالة التفعيل

## الملفات الموجودة
- `custom-bus-page.php` - صفحة عرض الرحلة المخصصة
- `bus-api.php` - API البحث (تم إضافة دالة search_specific_trip)
- `.htaccess` - قواعد إعادة التوجيه

## الملفات المحذوفة
- ❌ `add-custom-page.php` - صفحة الإدارة (محذوفة)
- ❌ `toggle-page-status.php` - API تبديل الحالة (محذوفة)

## كيفية العمل

### 1. عرض الصفحة المخصصة
- كل صفحة مربوطة برحلة محددة من جدول `bus_trips`
- البيانات تأتي مباشرة من قاعدة البيانات (المدن، المحطات، الأسعار، المواعيد)
- المستخدم يحتاج فقط لاختيار تاريخ السفر وعدد المسافرين

### 2. البحث عن الرحلة
- يتم البحث عن الرحلة المحددة بناءً على `trip_id`
- فحص توفر المقاعد للتاريخ المحدد
- فحص أيام العمل للرحلة
- عرض النتيجة مباشرة

### 3. الحجز
- الانتقال لصفحة الحجز مع بيانات الرحلة المحددة
- جميع البيانات محددة مسبقاً من الرحلة

## الصفحات المتاحة حالياً
1. **buses-مكة--to-الرياض** - من مكة المكرمة إلى الرياض (Trip ID: 131)
2. **buses-الرياض-to-مكة** - من الرياض إلى مكة (Trip ID: 143)  
3. **buses-مكة--to-المدينة** - من مكة المكرمة إلى المدينة (Trip ID: 144)

## إضافة صفحة جديدة
لإضافة صفحة مخصصة جديدة، أضف سجل في جدول `custom_bus_pages`:

```sql
INSERT INTO custom_bus_pages (trip_id, page_name, page_title, page_url, seo_title, seo_description, page_content) 
VALUES (
    [trip_id من جدول bus_trips],
    'اسم الصفحة',
    'عنوان الصفحة',
    'buses-city1-to-city2',
    'عنوان السيو',
    'وصف السيو',
    'محتوى إضافي'
);
```

## المميزات
- ✅ **بيانات حقيقية**: الأسعار والمحطات من قاعدة البيانات
- ✅ **نظام مبسط**: بدون صفحات إدارة معقدة
- ✅ **أداء سريع**: استعلام واحد لجلب بيانات الرحلة
- ✅ **سيو محسن**: عناوين ووصف مخصص لكل صفحة
- ✅ **تصميم متسق**: نفس تصميم الموقع الأساسي

## الاختبار
- اذهب إلى `test_final_system.php` لاختبار النظام
- جرب الروابط المتاحة
- اختبر البحث والحجز

## الدعم الفني
- تأكد من وجود رحلات في جدول `bus_trips`
- تأكد من تفعيل mod_rewrite في Apache
- راجع ملفات الأخطاء في حالة وجود مشاكل