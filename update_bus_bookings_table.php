<?php
include('webset.php');

echo "<h2>تحديث جدول bus_bookings لإضافة حقل الواتساب</h2>";

try {
    // إضافة حقل الواتساب
    $sql = "ALTER TABLE bus_bookings 
            ADD COLUMN passenger_whatsapp VARCHAR(20) AFTER passenger_phone";
    
    $db->exec($sql);
    echo "<div style='color: green; font-weight: bold; margin: 20px 0;'>✅ تم إضافة حقل passenger_whatsapp بنجاح!</div>";
    
    // تحديث البيانات الموجودة
    $sql2 = "UPDATE bus_bookings 
             SET passenger_whatsapp = passenger_phone 
             WHERE passenger_whatsapp IS NULL";
    
    $result = $db->exec($sql2);
    echo "<div style='color: blue; font-weight: bold; margin: 20px 0;'>✅ تم تحديث {$result} سجل موجود!</div>";
    
    // عرض هيكل الجدول الجديد
    $stmt = $db->query("DESCRIBE bus_bookings");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>هيكل الجدول الجديد:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم الحقل</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "<div style='color: orange; font-weight: bold; margin: 20px 0;'>⚠️ حقل passenger_whatsapp موجود بالفعل!</div>";
        
        // تحديث البيانات الموجودة فقط
        $sql2 = "UPDATE bus_bookings 
                 SET passenger_whatsapp = passenger_phone 
                 WHERE passenger_whatsapp IS NULL OR passenger_whatsapp = ''";
        
        $result = $db->exec($sql2);
        echo "<div style='color: blue; font-weight: bold; margin: 20px 0;'>✅ تم تحديث {$result} سجل موجود!</div>";
    } else {
        echo "<div style='color: red; font-weight: bold; margin: 20px 0;'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}
?>
