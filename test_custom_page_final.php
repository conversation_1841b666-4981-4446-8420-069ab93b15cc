<?php
include_once('config.php');

echo "<h2>اختبار الصفحة المخصصة النهائي</h2>";

try {
    // جلب صفحة مخصصة للاختبار
    $stmt = $db->prepare("SELECT cp.*, bt.* FROM custom_bus_pages cp 
                         JOIN bus_trips bt ON cp.trip_id = bt.id 
                         WHERE cp.is_active = 1 LIMIT 1");
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($page) {
        echo "<h3>✅ تم العثور على صفحة مخصصة للاختبار:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><td><strong>اسم الصفحة:</strong></td><td>{$page['page_name']}</td></tr>";
        echo "<tr><td><strong>رابط الصفحة:</strong></td><td><a href='custom-bus-page.php?page={$page['page_url']}' target='_blank'>{$page['page_url']}</a></td></tr>";
        echo "<tr><td><strong>من:</strong></td><td>{$page['from_city']}</td></tr>";
        echo "<tr><td><strong>إلى:</strong></td><td>{$page['to_city']}</td></tr>";
        echo "<tr><td><strong>محطة السفر:</strong></td><td>{$page['from_station']}</td></tr>";
        echo "<tr><td><strong>محطة الوصول:</strong></td><td>{$page['to_station']}</td></tr>";
        echo "<tr><td><strong>السعر:</strong></td><td>{$page['seat_price']} ريال</td></tr>";
        echo "<tr><td><strong>وقت المغادرة:</strong></td><td>{$page['departure_time']}</td></tr>";
        echo "</table>";
        
        echo "<h3>🧪 اختبار API البحث:</h3>";
        
        // اختبار API البحث
        $test_data = [
            'action' => 'search_specific_trip',
            'trip_id' => $page['trip_id'],
            'travel_date' => date('Y-m-d', strtotime('+1 day')),
            'passenger_count' => 2
        ];
        
        // محاكاة استدعاء API
        $_POST = json_encode($test_data);
        
        ob_start();
        include('bus-api.php');
        $api_response = ob_get_clean();
        
        echo "<h4>طلب API:</h4>";
        echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
        echo "<h4>استجابة API:</h4>";
        echo "<pre>" . $api_response . "</pre>";
        
        // اختبار الملفات المطلوبة
        echo "<h3>📁 فحص الملفات:</h3>";
        $files_to_check = [
            'custom-bus-page.php' => 'صفحة عرض الرحلة المخصصة',
            'bus-api.php' => 'API البحث والحجز',
            'bus-booking-form.php' => 'صفحة نموذج الحجز',
            '.htaccess' => 'قواعد إعادة التوجيه'
        ];
        
        foreach ($files_to_check as $file => $description) {
            if (file_exists($file)) {
                echo "✅ {$file} - {$description}<br>";
            } else {
                echo "❌ {$file} - {$description} (مفقود)<br>";
            }
        }
        
        echo "<h3>🔗 روابط الاختبار:</h3>";
        echo "<ul>";
        echo "<li><a href='custom-bus-page.php?page={$page['page_url']}' target='_blank'>اختبار الصفحة المخصصة</a></li>";
        echo "<li><a href='bus-booking.php' target='_blank'>الصفحة الأساسية للمقارنة</a></li>";
        echo "</ul>";
        
        echo "<h3>📋 خطوات الاختبار:</h3>";
        echo "<ol>";
        echo "<li>اذهب للصفحة المخصصة</li>";
        echo "<li>اختر تاريخ السفر (غداً أو بعد غد)</li>";
        echo "<li>اضغط 'أعرض الرحلات'</li>";
        echo "<li>جرب تعديل عدد المسافرين في الكارت</li>";
        echo "<li>اضغط 'احجز الآن'</li>";
        echo "</ol>";
        
    } else {
        echo "❌ لا توجد صفحات مخصصة نشطة للاختبار";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>