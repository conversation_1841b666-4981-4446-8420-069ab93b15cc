<?php
// جلب معرف الصفحة من الرابط
$page_url = $_GET['page'] ?? '';

if (empty($page_url)) {
    header('Location: bus-booking.php');
    exit;
}

include_once('webset.php');

// جلب بيانات الصفحة المخصصة مع بيانات الرحلة
try {
    $stmt = $db->prepare("SELECT cp.*, bt.from_city, bt.to_city, bt.from_station, bt.to_station, bt.seat_price, bt.departure_time, bt.arrival_time, bt.total_seats 
                         FROM custom_bus_pages cp 
                         JOIN bus_trips bt ON cp.trip_id = bt.id 
                         WHERE cp.page_url = ? AND cp.is_active = 1 AND bt.status = 1");
    $stmt->execute([$page_url]);
    $page_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page_data) {
        header('Location: error404.php');
        exit;
    }
    
} catch (Exception $e) {
    header('Location: error404.php');
    exit;
}

// تعيين بيانات الصفحة
$Title_page = $page_data['seo_title'];
$Description_page = $page_data['seo_description'];

include_once('header.php');
include_once('navbar.php');

// إضافة Font Awesome للأيقونات العصرية
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">';

// إضافة jQuery UI للتاريخ العربي
echo '<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">';
echo '<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>';

echo '
<section class="bus-booking-hero" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); min-height: 100vh; position: relative; overflow: hidden; padding-top: 120px;">
    <div class="container-fluid h-100">
        <div class="row align-items-center h-100">
            <!-- النص والعنوان مع صورة الباص -->
            <div class="col-lg-7 hero-text-section">
                <div class="hero-content text-white">
                    <h1 class="hero-title" style="font-size: 3.5rem; font-weight: bold; line-height: 1.2; margin-bottom: 20px;">' . $page_data['page_title'] . '</h1>
                    <div class="bus-image-container" style="position: relative; margin-top: 40px;">
                        <img src="'.$Site_URL.'/img/bus-image-ar.png" alt="Bus" class="bus-image" style="width: 100%; height: auto; border-radius: 15px;">
                    </div>
                </div>
            </div>

            <!-- نموذج الحجز -->
            <div class="col-lg-5 booking-section">
                <div class="booking-form-container" style="padding: 0px;">
                    <div class="booking-form-card" style="background: white; border-radius: 20px; padding: 30px; box-shadow: 0 20px 60px rgba(0,0,0,0.1);">
                        <!-- عنوان النموذج -->
                        <div class="form-header mb-4">
                            <div class="text-center">
                                <h4 style="color: #333; font-weight: bold; margin-bottom: 10px;">ابحث عن رحلتك</h4>
                                <p style="color: #666; font-size: 14px; margin-bottom: 10px;">من ' . $page_data['from_city'] . ' إلى ' . $page_data['to_city'] . '</p>
                            </div>
                        </div>

                        <form id="customBusSearchForm">
                            <!-- checkbox ذهاب فقط في أول النموذج -->
                            <div class="row mb-2">
                                <div class="col-12">
                                    <div class="form-check d-flex align-items-center" style="background: #fff; border: 1px solid #ddd; border-radius: 6px; padding: 8px 12px;">
                                        <input class="form-check-input" type="checkbox" id="oneWayOnlyCustom" name="one_way_only" value="1" checked style="margin: 0; margin-left: 8px;">
                                        <label class="form-check-label" for="oneWayOnlyCustom" style="font-weight: normal; color: #333; font-size: 14px; margin: 0; cursor: pointer;">
                                            ذهاب فقط
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- رابط تعديل الاتجاه -->
                            <div class="row mb-2">
                                <div class="col-12 text-start">
                                    <a href="bus-booking.php" class="edit-direction-link" style="color: #666; font-size: 14px; text-decoration: none; display: inline-flex; align-items: center; gap: 5px;">
                                        <i class="fas fa-edit" style="font-size: 12px;"></i>
                                        <span>تعديل الاتجاه</span>
                                    </a>
                                </div>
                            </div>

                            <!-- مدينة السفر (محددة مسبقاً) -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="selected-city-display" style="background: #e8f5e8; border: 2px solid #28a745; border-radius: 10px; padding: 12px; font-size: 16px; font-weight: bold; color: #28a745;">
                                        <span style="margin-left: 8px;color: #000;font-weight: 500;">سفر من</span>
                                        <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
                                        ' . $page_data['from_city'] . '
                                    </div>
                                    <input type="hidden" id="fromCity" name="from_city" value="' . $page_data['from_city'] . '">
                                </div>
                            </div>

                            <!-- مدينة الوصول (محددة مسبقاً) -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="selected-city-display" style="background: #fff8f0; border: 2px solid #ff9500; border-radius: 10px; padding: 12px; font-size: 16px; font-weight: bold; color: #ff9500;">
                                        
                                        <span style="margin-left: 8px;color: #000;font-weight: 500;">وصول إلى</span>
                                        <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
                                        ' . $page_data['to_city'] . '
                                    </div>
                                    <input type="hidden" id="toCity" name="to_city" value="' . $page_data['to_city'] . '">
                                </div>
                            </div>

                            <!-- محطات السفر والوصول -->
                            <div class="row mb-3" id="stationsRow">
                                <div class="col-12">
                                    <div class="station-display" style="display: flex;align-items: center;gap: 12px; margin-bottom: 10px;">
                                        <div style="width: 10px; height: 10px; background: #28a745; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                                        <span style="font-weight: 600; color: #333; font-size: 13px;">
                                            محطة السفر - <span style="color: #28a745;">' . $page_data['from_station'] . '</span>
                                        </span>
                                    </div>
                                    <div class="station-display" style="display: flex;align-items: center;gap: 12px;">
                                        <div style="width: 10px; height: 10px; background: #ff9500; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                                        <span style="font-weight: 600; color: #333; font-size: 13px;">
                                            محطة الوصول - <span style="color: #ff9500;">' . $page_data['to_station'] . '</span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- تاريخ السفر وعدد المسافرين -->
                            <div class="row mb-4">
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        <i class="fas fa-calendar-alt" style="margin-left: 8px; color: #ffc107; font-size: 16px;"></i>
                                        <span>تاريخ السفر</span>
                                    </label>
                                    <input type="text" class="form-control" id="travelDate" name="travel_date" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px; direction: rtl; text-align: right;" placeholder="اختر تاريخ السفر" readonly>
                                </div>
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        <i class="fas fa-users" style="margin-left: 8px; color: #17a2b8; font-size: 16px;"></i>
                                        <span>عدد المسافرين</span>
                                    </label>
                                    <div class="passenger-counter" style="display: flex; align-items: center; border: 2px solid #e9ecef; border-radius: 10px; padding: 8px;">
                                        <button type="button" class="btn-counter" id="decreaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">-</button>
                                        <input type="number" class="form-control text-center" id="passengerCount" name="passenger_count" value="1" min="1" max="10" style="border: none; font-size: 18px; font-weight: bold; background: transparent;" readonly>
                                        <button type="button" class="btn-counter" id="increaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">+</button>
                                    </div>
                                    <small class="text-muted" style="font-size: 12px;">يُنصح بحجز مقعد للأطفال مع ولي الأمر</small>
                                </div>
                            </div>

                            <!-- زر البحث -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-search w-100" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); color: white; border: none; border-radius: 12px; padding: 15px; font-size: 18px; font-weight: bold; transition: all 0.3s;">
                                        <i class="fas fa-search" style="margin-left: 8px;"></i>
                                        <span>أعرض الرحلات</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
                        <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">' . $page_data['page_content'] . '</p>

</section>



<!-- قسم عرض النتائج -->
<section id="searchResults" class="search-results-section" style="display: none; padding: 60px 0; background: #f8f9fa;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center mb-5" style="color: #333; font-weight: bold;">الرحلات المتاحة من ' . $page_data['from_city'] . ' إلى ' . $page_data['to_city'] . '</h2>
                <div id="tripsContainer" class="trips-container">
                    <!-- سيتم عرض الرحلات هنا -->
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bus-booking-hero {
    position: relative;
    margin-top: 0;
    z-index: 1;
}

.bus-booking-hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.selected-city-display {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.selected-city-display:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.form-select:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
    transform: translateY(-1px);
}

.form-select:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px !important;
}

.form-select {
    transition: all 0.3s ease;
}

.form-select:hover:not(:disabled) {
    border-color: #ff9500;
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.15);
}

.station-info {
    transition: all 0.3s ease;
}

.station-info:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.form-label i {
    transition: all 0.3s ease;
}

.form-label:hover i {
    transform: scale(1.1);
}

.btn-search i {
    transition: all 0.3s ease;
}

.btn-search:hover i {
    transform: rotate(360deg);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تصميم كارت الرحلات المبسط */
.trip-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.trip-card:hover {
    border-color: #ff9500;
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
}

.trip-card .date-time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.trip-card .station-box {
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border: 1px solid;
}

.trip-card .departure-box {
    background: #e8f5e8;
    border-color: #28a745;
}

.trip-card .arrival-box {
    background: #fff8f0;
    border-color: #ff9500;
}

.price-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.price-display {
    font-size: 20px;
    font-weight: bold;
    color: #28a745;
}

.book-btn {
    background: #ff9500;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 25px;
    font-weight: bold;
    transition: background 0.2s ease;
}

.book-btn:hover {
    background: #ff7b00;
    color: white;
}

/* تحسينات إضافية للتصميم */
.station-display {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    padding: 8px;
    border-radius: 5px;
    background: #f8f9fa;
}

.station-display:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* إصلاح مشكلة التداخل مع الـ navbar */
body {
    padding-top: 0 !important;
}

.navbar {
    z-index: 1050 !important;
}

.bus-booking-hero {
    padding-top: 120px !important;
}

/* تصميم checkbox ذهاب فقط */
.form-check {
    transition: all 0.2s ease;
}

.form-check:hover {
    border-color: #bbb !important;
}

.form-check-input {
    border: 1px solid #ccc;
    border-radius: 3px;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.form-check-label {
    user-select: none;
}

/* تصميم رابط تعديل الاتجاه */
.edit-direction-link {
    transition: all 0.3s ease;
}

.edit-direction-link:hover {
    color: #ff9500 !important;
    text-decoration: none !important;
    transform: translateX(-3px);
}

.edit-direction-link:hover i {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
        font-size: 23px !important;
        text-align: center;
        color: #fff;
        margin-top: 9px;
        margin-bottom: -28px !important;
    }

    .bus-booking-hero {
        padding-top: 50px !important;
    }

    .booking-form-card {
        margin: 5px;
        padding: 20px !important;
    }

    .col-6 .form-label {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .col-6 .form-select,
    .col-6 .form-control {
        padding: 8px;
        font-size: 14px;
    }

    .passenger-counter {
        padding: 4px !important;
    }

    .btn-counter {
        width: 30px !important;
        height: 30px !important;
        font-size: 14px !important;
    }
}

/* تحسين مظهر jQuery UI Datepicker */
.ui-datepicker {
    font-family: Arial, "Segoe UI", sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
    border-radius: 10px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
    border: none !important;
}

.ui-datepicker-header {
    background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px 10px 0 0 !important;
    padding: 10px !important;
}

.ui-datepicker-title {
    color: white !important;
    font-weight: bold !important;
}

.ui-datepicker-prev, .ui-datepicker-next {
    background: rgba(255,255,255,0.2) !important;
    border: none !important;
    border-radius: 5px !important;
}

.ui-datepicker-prev:hover, .ui-datepicker-next:hover {
    background: rgba(255,255,255,0.3) !important;
}

.ui-datepicker td {
    text-align: center !important;
    padding: 2px !important;
}

.ui-datepicker td a {
    padding: 8px !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    color: #333 !important;
}

.ui-datepicker td a:hover {
    background: #ff9500 !important;
    color: white !important;
}

.ui-datepicker .ui-state-active {
    background: #ff7b00 !important;
    color: white !important;
}
</style>

<script>
// متغير عام لمعرف الرحلة
const TRIP_ID = "' . $page_data['trip_id'] . '";

document.addEventListener("DOMContentLoaded", function() {
    // إعداد jQuery UI Datepicker العربي
    $.datepicker.regional["ar"] = {
        closeText: "إغلاق",
        prevText: "السابق",
        nextText: "التالي",
        currentText: "اليوم",
        monthNames: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
        monthNamesShort: ["1", "2", "3", "4", "5", "6",
            "7", "8", "9", "10", "11", "12"],
        dayNames: ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"],
        dayNamesShort: ["أحد", "اثن", "ثلاث", "أربع", "خميس", "جمع", "سبت"],
        dayNamesMin: ["ح", "ن", "ث", "ر", "خ", "ج", "س"],
        weekHeader: "أسبوع",
        dateFormat: "yy-mm-dd",
        firstDay: 0,
        isRTL: true,
        showMonthAfterYear: false,
        yearSuffix: ""
    };
    $.datepicker.setDefaults($.datepicker.regional["ar"]);

    // تطبيق datepicker على مربع التاريخ
    $("#travelDate").datepicker({
        dateFormat: "yy-mm-dd",
        minDate: 0,
        changeMonth: true,
        changeYear: true,
        showAnim: "slideDown"
    });

    // تعيين التاريخ الحالي كقيمة افتراضية
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const formattedDate = year + "-" + month + "-" + day;
    $("#travelDate").val(formattedDate);

    // أحداث النموذج
    setupFormEvents();
    

    
    // إعداد أحداث النموذج
    function setupFormEvents() {
        
        // أحداث عداد المسافرين
        document.getElementById("increaseBtn").addEventListener("click", function() {
            const input = document.getElementById("passengerCount");
            const currentValue = parseInt(input.value);
            if (currentValue < 10) {
                input.value = currentValue + 1;
                clearResults(); // مسح النتائج عند تغيير عدد المسافرين
            }
        });
        
        document.getElementById("decreaseBtn").addEventListener("click", function() {
            const input = document.getElementById("passengerCount");
            const currentValue = parseInt(input.value);
            if (currentValue > 1) {
                input.value = currentValue - 1;
                clearResults(); // مسح النتائج عند تغيير عدد المسافرين
            }
        });
        
        // مسح النتائج عند تغيير التاريخ
        document.getElementById("travelDate").addEventListener("change", function() {
            clearResults();
        });

        // إرسال النموذج
        document.getElementById("customBusSearchForm").addEventListener("submit", function(e) {
            e.preventDefault();
            searchTrips();
        });
    }
    

    
    // البحث عن الرحلات
    function searchTrips() {
        const formData = {
            action: "search_specific_trip",
            trip_id: TRIP_ID,
            travel_date: document.getElementById("travelDate").value,
            passenger_count: document.getElementById("passengerCount").value
        };
        
        // التحقق من البيانات المطلوبة
        if (!formData.travel_date) {
            alert("يرجى اختيار تاريخ السفر");
            return;
        }
        
        // عرض مؤشر التحميل
        const searchResults = document.getElementById("searchResults");
        const tripsContainer = document.getElementById("tripsContainer");
        
        tripsContainer.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-3">جاري البحث عن الرحلات المتاحة...</p>
            </div>
        `;
        
        searchResults.style.display = "block";
        searchResults.scrollIntoView({ behavior: "smooth" });
        
        // إرسال طلب البحث
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTrips(data.trips);
            } else {
                tripsContainer.innerHTML = `
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${data.message || "لم يتم العثور على رحلات متاحة"}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error("خطأ في البحث:", error);
            tripsContainer.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-times-circle"></i>
                    حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.
                </div>
            `;
        });
    }
    
    // عرض الرحلات
    function displayTrips(trips) {
        const tripsContainer = document.getElementById("tripsContainer");
        const travelDate = document.getElementById("travelDate").value;
        const passengerCount = parseInt(document.getElementById("passengerCount").value);
        
        if (trips.length === 0) {
            tripsContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-bus" style="font-size: 4rem; color: #ff9500;"></i>
                    </div>
                    <h4>لا توجد رحلات متاحة</h4>
                    <p class="text-muted">لم يتم العثور على رحلات متاحة للتاريخ المحدد</p>
                </div>
            `;
            return;
        }
        
        let html = "";
        trips.forEach((trip, index) => {
            const totalPrice = (parseFloat(trip.seat_price) * passengerCount).toFixed(2);

            html += `
                <div class="trip-card">
                    <!-- 1. تاريخ الرحلة وموعد المغادرة على نفس السطر -->
                    <div style="display: flex; justify-content: space-between; align-items: center; background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-calendar-alt" style="color: #ff9500; font-size: 16px;"></i>
                            <span style="font-weight: 600; color: #333; font-size: 15px;">${formatDate(travelDate)}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-clock" style="color: #17a2b8; font-size: 16px;"></i>
                            <span style="font-weight: bold; color: #17a2b8; font-size: 26px;">${formatTime(trip.departure_time)}</span>
                        </div>
                    </div>

                    <!-- 2. مربع مدينة السفر مع محطة الانطلاق -->
                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 8px; padding: 12px; margin-bottom: 10px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <div style="width: 10px; height: 10px; background: #28a745; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: bold; color: #28a745; font-size: 16px; margin-bottom: 5px;">سفر من ${trip.from_city}</div>
                                <div style="color: #000000;font-size: 14px;line-height: 1.4;border-radius: 4px;margin-top: 5px;">${trip.from_station}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 3. مربع مدينة الوصول مع محطة الوصول -->
                    <div style="background: #fff8f0; border: 1px solid #ff9500; border-radius: 8px; padding: 12px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <div style="width: 10px; height: 10px; background: #ff9500; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: bold; color: #ff9500; font-size: 16px; margin-bottom: 5px;">وصول إلى ${trip.to_city}</div>
                                <div style="color: #000000;font-size: 14px;line-height: 1.4;border-radius: 4px;margin-top: 5px;">${trip.to_station}</div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المسافرين والسعر المبسط -->
                    <div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <!-- عدد المسافرين -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333; font-size: 14px;">عدد المسافرين:</span>
                            <div style="display: flex; align-items: center; background: white; border-radius: 5px; border: 1px solid #ddd;">
                                <button type="button" onclick="updatePassengerCount(${index}, -1)" style="background: none; border: none; width: 30px; height: 30px; font-size: 14px; font-weight: bold; color: #666; cursor: pointer;">-</button>
                                <input type="number" id="passenger-count-${index}" value="${passengerCount}" min="1" max="10" style="border: none; width: 40px; text-align: center; font-size: 14px; font-weight: bold; background: transparent;" readonly>
                                <button type="button" onclick="updatePassengerCount(${index}, 1)" style="background: none; border: none; width: 30px; height: 30px; font-size: 14px; font-weight: bold; color: #666; cursor: pointer;">+</button>
                            </div>
                        </div>

                        <!-- سعر المقعد -->
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: bold; color: #333; font-size: 14px;">سعر المقعد:</span>
                            <span style="font-size: 16px; font-weight: bold; color: #28a745;">${trip.seat_price} ريال</span>
                        </div>
                    </div>

                    <div class="price-section">
                        <div>
                            <div class="price-display" id="total-price-${index}">${totalPrice} ريال</div>
                            <div style="font-size: 14px; color: #666;" id="passenger-text-${index}">لـ ${passengerCount} مسافر</div>
                        </div>
                        <button class="book-btn" id="book-btn-${index}" onclick="bookTrip(${trip.id}, \'${travelDate}\', ${passengerCount}, ${totalPrice})">
                            احجز الآن
                        </button>
                    </div>
                </div>
            `;
        });

        tripsContainer.innerHTML = html;

        // حفظ بيانات الرحلات للاستخدام في تحديث الأسعار
        window.currentTripsData = trips;
    }
    
    // تنسيق التاريخ بالعربي مع التاريخ الميلادي
    function formatDate(dateString) {
        const date = new Date(dateString);

        // أسماء الأيام بالعربية
        const arabicDays = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];

        // أسماء الأشهر الميلادية بالعربية
        const arabicMonths = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ];

        // الحصول على اليوم والتاريخ
        const dayName = arabicDays[date.getDay()];
        const day = date.getDate();
        const month = arabicMonths[date.getMonth()];
        const year = date.getFullYear();

        // تنسيق التاريخ: اليوم، اليوم الشهر السنة
        return `${dayName}، ${day} ${month} ${year}`;
    }

    // تنسيق الوقت
    function formatTime(timeString) {
        const time = new Date("2000-01-01 " + timeString);
        return time.toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true
        });
    }

    // دالة مسح النتائج عند تغيير الاختيارات
    function clearResults() {
        const searchResults = document.getElementById("searchResults");
        if (searchResults) {
            searchResults.style.display = "none";
        }
        const tripsContainer = document.getElementById("tripsContainer");
        if (tripsContainer) {
            tripsContainer.innerHTML = "";
        }
    }
});

// دالة مسح النتائج (عامة)
function clearResults() {
    const searchResults = document.getElementById("searchResults");
    if (searchResults) {
        searchResults.style.display = "none";
    }
    const tripsContainer = document.getElementById("tripsContainer");
    if (tripsContainer) {
        tripsContainer.innerHTML = "";
    }
}

// دالة تحديث عدد المسافرين
function updatePassengerCount(tripIndex, change) {
    const passengerInput = document.getElementById(`passenger-count-${tripIndex}`);
    const totalPriceElement = document.getElementById(`total-price-${tripIndex}`);
    const passengerTextElement = document.getElementById(`passenger-text-${tripIndex}`);
    const bookButton = document.getElementById(`book-btn-${tripIndex}`);

    let currentCount = parseInt(passengerInput.value);
    let newCount = currentCount + change;

    // التحقق من الحدود
    if (newCount < 1) newCount = 1;
    if (newCount > 10) newCount = 10;

    // تحديث العدد
    passengerInput.value = newCount;

    // الحصول على سعر المقعد الواحد من البيانات المخزنة
    const tripData = window.currentTripsData[tripIndex];
    const seatPrice = parseFloat(tripData.seat_price);
    const newTotalPrice = (seatPrice * newCount).toFixed(2);

    // تحديث السعر الإجمالي
    totalPriceElement.textContent = newTotalPrice + " ريال";

    // تحديث نص المسافرين
    passengerTextElement.textContent = `لـ ${newCount} مسافر`;

    // تحديث زر الحجز
    const travelDate = document.getElementById("travelDate").value;
    bookButton.setAttribute("onclick", "bookTrip(" + tripData.id + ", \'" + travelDate + "\', " + newCount + ", " + newTotalPrice + ")");

    // تأثير بصري للتغيير
    totalPriceElement.style.transform = "scale(1.1)";
    totalPriceElement.style.color = "#ff9500";
    setTimeout(function() {
        totalPriceElement.style.transform = "scale(1)";
        totalPriceElement.style.color = "#28a745";
    }, 200);
}

// دالة حجز الرحلة
function bookTrip(tripId, travelDate, passengerCount, totalPrice) {
    // إنشاء نموذج الحجز
    const bookingData = {
        trip_id: tripId,
        travel_date: travelDate,
        passenger_count: passengerCount,
        total_price: totalPrice
    };

    // تحويل إلى صفحة الحجز
    const encodedData = encodeURIComponent(JSON.stringify(bookingData));
    window.location.href = `bus-booking-form.php?data=${encodedData}`;
}
</script>';

include_once('footer.php');
?>