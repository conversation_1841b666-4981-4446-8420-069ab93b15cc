<?php
// صفحة اختبار لعرض تحسينات تصميم تفاصيل الرحلة
$Title_page = 'اختبار تصميم إكمال حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// بيانات تجريبية للاختبار
$booking_data = [
    'trip_id' => 1,
    'travel_date' => '2024-12-25',
    'passenger_count' => 2,
    'total_price' => 150
];

$trip = [
    'id' => 1,
    'from_city' => 'الرياض',
    'to_city' => 'مكة المكرمة',
    'from_station' => 'محطة الرياض المركزية',
    'to_station' => 'محطة مكة الحرم',
    'departure_address' => 'شارع الملك فهد، حي العليا، الرياض',
    'arrival_address' => 'طريق الحرم، حي العزيزية، مكة المكرمة',
    'departure_time' => '08:30:00',
    'seat_price' => 75
];

echo '
<div class="container" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="page-header" style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h3 style="color: #333; margin: 0; text-align: center;">إكمال حجز الباص</h3>
                <p style="color: #666; margin: 5px 0 0 0; text-align: center; font-size: 14px;">أكمل بياناتك لتأكيد الحجز</p>
            </div>

            <!-- تفاصيل الرحلة -->
            <div class="trip-details" style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">
                    تفاصيل الرحلة
                </h4>

                        <!-- تاريخ الرحلة وموعد المغادرة -->
                        <div style="display: flex; justify-content: space-between; align-items: center; background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-calendar-alt" style="color: #ff9500; font-size: 16px;"></i>';

// تحويل التاريخ إلى العربية
$date = strtotime($booking_data['travel_date']);
$arabic_days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
$arabic_months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$day_name = $arabic_days[date('w', $date)];
$day = date('d', $date);
$month = $arabic_months[date('n', $date)];
$year = date('Y', $date);
$arabic_date = $day_name . '، ' . $day . ' ' . $month . ' ' . $year;

echo '                                <span style="font-weight: 600; color: #333; font-size: 15px;">'.$arabic_date.'</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-clock" style="color: #17a2b8; font-size: 16px;"></i>
                                <span style="font-weight: bold; color: #17a2b8; font-size: 22px;">'.date('h:i A', strtotime($trip['departure_time'])).'</span>
                            </div>
                        </div>

                        <!-- محطة السفر -->
                        <div style="background: #f0f8f0; border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin-bottom: 12px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div style="width: 8px; height: 8px; background: #28a745; border-radius: 50%;"></div>
                                <span style="font-size: 13px; color: #28a745; font-weight: 600;">سفر من</span>
                                <span style="font-weight: bold; color: #28a745; font-size: 18px;">'.$trip['from_city'].'</span>
                            </div>
                            <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                                <i class="fas fa-building" style="margin-left: 6px; color: #28a745;"></i>
                                '.$trip['from_station'].'
                            </div>
                            <div style="color: #666; font-size: 13px;">
                                <i class="fas fa-map-pin" style="margin-left: 6px; color: #28a745;"></i>
                                '.$trip['departure_address'].'
                            </div>
                        </div>

                        <!-- محطة الوصول -->
                        <div style="background: #fef9f3; border: 1px solid #ff9500; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div style="width: 8px; height: 8px; background: #ff9500; border-radius: 50%;"></div>
                                <span style="font-size: 13px; color: #ff9500; font-weight: 600;">وصول إلى</span>
                                <span style="font-weight: bold; color: #ff9500; font-size: 18px;">'.$trip['to_city'].'</span>
                            </div>
                            <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                                <i class="fas fa-building" style="margin-left: 6px; color: #ff9500;"></i>
                                '.$trip['to_station'].'
                            </div>
                            <div style="color: #666; font-size: 13px;">
                                <i class="fas fa-map-pin" style="margin-left: 6px; color: #ff9500;"></i>
                                '.$trip['arrival_address'].'
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div style="background: #f8f9fa; border-radius: 6px; padding: 12px; text-align: center;">
                                    <div style="font-size: 13px; color: #666; margin-bottom: 4px;">عدد المسافرين</div>
                                    <div style="font-weight: bold; color: #333; font-size: 16px;">'.$booking_data['passenger_count'].' مسافر</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div style="background: #28a745; border-radius: 6px; padding: 12px; text-align: center; color: white;">
                                    <div style="font-size: 13px; margin-bottom: 4px; opacity: 0.9;">إجمالي المبلغ</div>
                                    <div style="font-weight: bold; font-size: 18px;">'.$booking_data['total_price'].' ريال</div>
                                </div>
                            </div>
                        </div>
                    </div>

            <!-- رسالة تأكيد -->
            <div class="alert alert-success text-center">
                <h5>✅ تم تحسين التصميم بنجاح!</h5>
                <p class="mb-0">التصميم أصبح بسيط وسريع للموبايل مع التاريخ باللغة العربية</p>
            </div>

        </div>
    </div>
</div>';

?>

<style>
/* تصميم بسيط وسريع للموبايل */
@media (max-width: 768px) {
    .container {
        margin: 10px !important;
        padding: 0 10px !important;
    }

    .page-header, .trip-details {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .page-header h3 {
        font-size: 16px !important;
    }

    .trip-details h4 {
        font-size: 16px !important;
    }
}
</style>

<?php
include('footer.php');
?>
