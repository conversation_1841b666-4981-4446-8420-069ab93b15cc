<?php
$Title_page = 'إكمال حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود البيانات
if (!isset($_GET['data'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = json_decode(urldecode($_GET['data']), true);
if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

// جلب تفاصيل الرحلة
$trip_id = $booking_data['trip_id'];
$stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
$stmt->execute([$trip_id]);
$trip = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$trip) {
    header('Location: bus-booking.php');
    exit;
}

echo '
<section class="booking-form-section" style="padding: 60px 0; background: #f8f9fa;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="booking-form-container" style="background: white; border-radius: 20px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">

                    <!-- عنوان الصفحة -->
                    <div class="text-center mb-5">
                        <h2 style="color: #333; font-weight: bold; margin-bottom: 10px;">إكمال حجز الباص</h2>
                        <p style="color: #666;">أكمل بياناتك لتأكيد الحجز</p>
                    </div>

                    <!-- تفاصيل الرحلة -->
                    <div class="trip-summary-card" style="background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: 1px solid #e9ecef;">
                        <h4 style="color: #333; margin-bottom: 25px; display: flex; align-items: center; font-weight: bold;">
                            <span style="margin-left: 10px; font-size: 24px;">🚌</span>
                            تفاصيل الرحلة
                        </h4>

                        <!-- تاريخ الرحلة وموعد المغادرة -->
                        <div style="display: flex; justify-content: space-between; align-items: center; background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-calendar-alt" style="color: #ff9500; font-size: 16px;"></i>';

// تحويل التاريخ إلى العربية
$date = strtotime($booking_data['travel_date']);
$arabic_days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
$arabic_months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$day_name = $arabic_days[date('w', $date)];
$day = date('d', $date);
$month = $arabic_months[date('n', $date)];
$year = date('Y', $date);
$arabic_date = $day_name . '، ' . $day . ' ' . $month . ' ' . $year;

echo '                                <span style="font-weight: 600; color: #333; font-size: 15px;">'.$arabic_date.'</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-clock" style="color: #17a2b8; font-size: 16px;"></i>
                                <span style="font-weight: bold; color: #17a2b8; font-size: 22px;">'.date('h:i A', strtotime($trip['departure_time'])).'</span>
                            </div>
                        </div>

                        <!-- محطة السفر -->
                        <div style="background: #f0f8f0; border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin-bottom: 12px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div style="width: 8px; height: 8px; background: #28a745; border-radius: 50%;"></div>
                                <span style="font-size: 13px; color: #28a745; font-weight: 600;">سفر من</span>
                            </div>
                            <div style="font-weight: bold; color: #28a745; font-size: 18px; margin-bottom: 6px;">'.$trip['from_city'].'</div>';

// إضافة محطة الانطلاق إذا كانت متوفرة
if (!empty($trip['from_station'])) {
    echo '                            <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                                <i class="fas fa-building" style="margin-left: 6px; color: #28a745;"></i>
                                '.$trip['from_station'].'
                            </div>';
}

// إضافة عنوان محطة الانطلاق إذا كان متوفراً
if (!empty($trip['departure_address'])) {
    echo '                            <div style="color: #666; font-size: 13px;">
                                <i class="fas fa-map-pin" style="margin-left: 6px; color: #28a745;"></i>
                                '.$trip['departure_address'].'
                            </div>';
}

echo '                        </div>

                        <!-- محطة الوصول -->
                        <div style="background: #fef9f3; border: 1px solid #ff9500; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div style="width: 8px; height: 8px; background: #ff9500; border-radius: 50%;"></div>
                                <span style="font-size: 13px; color: #ff9500; font-weight: 600;">وصول إلى</span>
                            </div>
                            <div style="font-weight: bold; color: #ff9500; font-size: 18px; margin-bottom: 6px;">'.$trip['to_city'].'</div>';

// إضافة محطة الوصول إذا كانت متوفرة
if (!empty($trip['to_station'])) {
    echo '                            <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                                <i class="fas fa-building" style="margin-left: 6px; color: #ff9500;"></i>
                                '.$trip['to_station'].'
                            </div>';
}

// إضافة عنوان محطة الوصول إذا كان متوفراً
if (!empty($trip['arrival_address'])) {
    echo '                            <div style="color: #666; font-size: 13px;">
                                <i class="fas fa-map-pin" style="margin-left: 6px; color: #ff9500;"></i>
                                '.$trip['arrival_address'].'
                            </div>';
}

echo '                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div style="background: #f8f9fa; border-radius: 6px; padding: 12px; text-align: center;">
                                    <div style="font-size: 13px; color: #666; margin-bottom: 4px;">عدد المسافرين</div>
                                    <div style="font-weight: bold; color: #333; font-size: 16px;">'.$booking_data['passenger_count'].' مسافر</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div style="background: #28a745; border-radius: 6px; padding: 12px; text-align: center; color: white;">
                                    <div style="font-size: 13px; margin-bottom: 4px; opacity: 0.9;">إجمالي المبلغ</div>
                                    <div style="font-weight: bold; font-size: 18px;">'.$booking_data['total_price'].' ريال</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج البيانات الشخصية -->
                    <form id="bookingForm" method="post" action="process-bus-booking.php">
                        <input type="hidden" name="trip_id" value="'.$trip_id.'">
                        <input type="hidden" name="travel_date" value="'.$booking_data['travel_date'].'">
                        <input type="hidden" name="seats_count" value="'.$booking_data['passenger_count'].'">
                        <input type="hidden" name="total_amount" value="'.$booking_data['total_price'].'">

                        <h4 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                            <span style="margin-left: 10px;">👤</span>
                            البيانات الشخصية
                        </h4>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">الاسم الكامل *</label>
                                <input type="text" class="form-control" name="full_name" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="أدخل الاسم الكامل">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="05xxxxxxxx">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم واتساب *</label>
                                <input type="tel" class="form-control" name="whatsapp" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="05xxxxxxxx">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label" style="font-weight: 600; color: #333;">ملاحظات إضافية</label>
                            <textarea class="form-control" name="notes" rows="3" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                        </div>

                        <!-- شروط الخدمة -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms" style="color: #666;">
                                    أوافق على <a href="terms-and-conditions.php" target="_blank" style="color: #ff9500;">الشروط والأحكام</a> و <a href="privacy-policy.php" target="_blank" style="color: #ff9500;">سياسة الخصوصية</a>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="bus-booking.php" class="btn btn-outline-secondary w-100" style="border-radius: 10px; padding: 12px; font-weight: bold;">
                                    العودة للبحث
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="submit" class="btn btn-primary w-100" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); border: none; border-radius: 10px; padding: 12px; font-weight: bold;">
                                    تأكيد الحجز
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>';

?>

<style>
/* تصميم مبسط وخفيف */
.trip-summary-card {
    transition: box-shadow 0.2s ease;
}

.trip-summary-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

/* تحسين النماذج */
.form-control:focus, .form-select:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
}

/* تحسين الأزرار */
.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    .booking-form-container {
        margin: 15px;
        padding: 20px !important;
    }

    .trip-summary-card {
        padding: 15px !important;
    }

    .trip-summary-card > div:nth-child(2) {
        flex-direction: column !important;
        gap: 10px !important;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .trip-summary-card h4 {
        font-size: 16px !important;
    }
}
</style>

<?php

?>

<script>
// تأثيرات تفاعلية عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function() {
    // تأثير الظهور التدريجي للعناصر
    const elements = document.querySelectorAll(".trip-summary-card, .booking-form-container");
    elements.forEach((element, index) => {
        element.style.opacity = "0";
        element.style.transform = "translateY(30px)";
        element.style.transition = "all 0.6s ease";

        setTimeout(() => {
            element.style.opacity = "1";
            element.style.transform = "translateY(0)";
        }, index * 200);
    });

    // تأثير النبضة للأيقونات
    const icons = document.querySelectorAll(".fas");
    icons.forEach(icon => {
        icon.addEventListener("mouseenter", function() {
            this.style.transform = "scale(1.2) rotate(5deg)";
        });

        icon.addEventListener("mouseleave", function() {
            this.style.transform = "scale(1) rotate(0deg)";
        });
    });

    // تأثير تفاعلي لصناديق المعلومات
    const infoBoxes = document.querySelectorAll(".info-box");
    infoBoxes.forEach(box => {
        box.addEventListener("mouseenter", function() {
            this.style.transform = "translateY(-5px) scale(1.02)";
        });

        box.addEventListener("mouseleave", function() {
            this.style.transform = "translateY(0) scale(1)";
        });
    });
});

// معالج إرسال النموذج
document.getElementById("bookingForm").addEventListener("submit", function(e) {
    const submitBtn = this.querySelector("button[type=submit]");
    const originalText = submitBtn.innerHTML;

    // تأثير التحميل
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري التأكيد...';
    submitBtn.disabled = true;
    submitBtn.style.background = "linear-gradient(135deg, #6c757d 0%, #5a6268 100%)";

    // تأثير بصري إضافي
    const tripCard = document.querySelector(".trip-summary-card");
    if (tripCard) {
        tripCard.style.opacity = "0.7";
        tripCard.style.transform = "scale(0.98)";
    }
});

// تأثير التحقق من صحة النموذج
const formInputs = document.querySelectorAll("input[required], textarea");
formInputs.forEach(input => {
    input.addEventListener("blur", function() {
        if (this.value.trim() === "") {
            this.style.borderColor = "#dc3545";
            this.style.boxShadow = "0 0 0 0.2rem rgba(220, 53, 69, 0.25)";
        } else {
            this.style.borderColor = "#28a745";
            this.style.boxShadow = "0 0 0 0.2rem rgba(40, 167, 69, 0.25)";
        }
    });

    input.addEventListener("focus", function() {
        this.style.borderColor = "#ff9500";
        this.style.boxShadow = "0 0 0 0.2rem rgba(255, 149, 0, 0.25)";
    });
});
</script>

<?php

include('footer.php');
?>

