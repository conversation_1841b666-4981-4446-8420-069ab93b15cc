<?php
$Title_page = 'إكمال حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود البيانات
if (!isset($_GET['data'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = json_decode(urldecode($_GET['data']), true);
if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

// جلب تفاصيل الرحلة
$trip_id = $booking_data['trip_id'];
$stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
$stmt->execute([$trip_id]);
$trip = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$trip) {
    header('Location: bus-booking.php');
    exit;
}

echo '
<div class="container" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="page-header" style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h3 style="color: #333; margin: 0; text-align: center;">إكمال حجز الباص</h3>
                <p style="color: #666; margin: 5px 0 0 0; text-align: center; font-size: 14px;">أكمل بياناتك لتأكيد الحجز</p>
            </div>

            <!-- تفاصيل الرحلة -->
            <div class="trip-details" style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">
                    🚌 تفاصيل الرحلة
                </h4>

                <!-- التاريخ والوقت -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">📅 تاريخ السفر</div>';

// تحويل التاريخ إلى العربية
$date = strtotime($booking_data['travel_date']);
$arabic_days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
$arabic_months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$day_name = $arabic_days[date('w', $date)];
$day = date('d', $date);
$month = $arabic_months[date('n', $date)];
$year = date('Y', $date);
$arabic_date = $day_name . '، ' . $day . ' ' . $month . ' ' . $year;

echo '                            <div style="font-weight: 600; color: #333; font-size: 14px;">'.$arabic_date.'</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">🕐 وقت المغادرة</div>
                            <div style="font-weight: bold; color: #17a2b8; font-size: 16px;">'.date('h:i A', strtotime($trip['departure_time'])).'</div>
                        </div>
                    </div>
                </div>

                <!-- محطة السفر -->
                <div style="background: #f0f8f0; border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin-bottom: 12px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <div style="width: 8px; height: 8px; background: #28a745; border-radius: 50%;"></div>
                        <span style="font-size: 13px; color: #28a745; font-weight: 600;">سفر من</span>
                    </div>
                    <div style="font-weight: bold; color: #28a745; font-size: 18px; margin-bottom: 6px;">'.$trip['from_city'].'</div>';

// إضافة محطة الانطلاق إذا كانت متوفرة
if (!empty($trip['from_station'])) {
    echo '                    <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                        <i class="fas fa-building" style="margin-left: 6px; color: #28a745;"></i>
                        '.$trip['from_station'].'
                    </div>';
}

// إضافة عنوان محطة الانطلاق إذا كان متوفراً
if (!empty($trip['departure_address'])) {
    echo '                    <div style="color: #666; font-size: 13px;">
                        <i class="fas fa-map-pin" style="margin-left: 6px; color: #28a745;"></i>
                        '.$trip['departure_address'].'
                    </div>';
}

echo '                </div>

                <!-- محطة الوصول -->
                <div style="background: #fef9f3; border: 1px solid #ff9500; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <div style="width: 8px; height: 8px; background: #ff9500; border-radius: 50%;"></div>
                        <span style="font-size: 13px; color: #ff9500; font-weight: 600;">وصول إلى</span>
                    </div>
                    <div style="font-weight: bold; color: #ff9500; font-size: 18px; margin-bottom: 6px;">'.$trip['to_city'].'</div>';

// إضافة محطة الوصول إذا كانت متوفرة
if (!empty($trip['to_station'])) {
    echo '                    <div style="color: #555; font-size: 14px; margin-bottom: 4px;">
                        <i class="fas fa-building" style="margin-left: 6px; color: #ff9500;"></i>
                        '.$trip['to_station'].'
                    </div>';
}

// إضافة عنوان محطة الوصول إذا كان متوفراً
if (!empty($trip['arrival_address'])) {
    echo '                    <div style="color: #666; font-size: 13px;">
                        <i class="fas fa-map-pin" style="margin-left: 6px; color: #ff9500;"></i>
                        '.$trip['arrival_address'].'
                    </div>';
}

echo '                </div>
                    <div class="col-md-6 mb-2">
                        <div style="background: #fef9f3; border-left: 4px solid #ff9500; padding: 12px; border-radius: 4px;">
                            <div style="font-size: 12px; color: #ff9500; margin-bottom: 4px;">🔴 وصول إلى</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">'.$trip['to_city'].'</div>';

// إضافة محطة الوصول إذا كانت متوفرة
if (!empty($trip['to_station'])) {
    echo '                            <div style="color: #555; font-size: 13px; margin-bottom: 2px;">'.$trip['to_station'].'</div>';
}

// إضافة عنوان محطة الوصول إذا كان متوفراً
if (!empty($trip['arrival_address'])) {
    echo '                            <div style="color: #666; font-size: 12px;">'.$trip['arrival_address'].'</div>';
}

echo '                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row">
                    <div class="col-6">
                        <div style="background: #f8f9fa; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">👥 عدد المسافرين</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px;">'.$booking_data['passenger_count'].' مسافر</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div style="background: #28a745; border-radius: 6px; padding: 12px; text-align: center; color: white;">
                            <div style="font-size: 12px; margin-bottom: 4px;">💰 إجمالي المبلغ</div>
                            <div style="font-weight: bold; font-size: 18px;">'.$booking_data['total_price'].' ريال</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج البيانات الشخصية -->
            <div class="booking-form" style="background: white; border-radius: 8px; padding: 20px; border: 1px solid #e9ecef;">
                <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">
                    👤 البيانات الشخصية
                </h4>

                <form id="bookingForm" method="post" action="process-bus-booking.php">
                    <input type="hidden" name="trip_id" value="'.$trip_id.'">
                    <input type="hidden" name="travel_date" value="'.$booking_data['travel_date'].'">
                    <input type="hidden" name="seats_count" value="'.$booking_data['passenger_count'].'">
                    <input type="hidden" name="total_amount" value="'.$booking_data['total_price'].'">

                    <div class="mb-3">
                        <label class="form-label" style="font-weight: 600; color: #333; font-size: 14px;">الاسم الكامل *</label>
                        <input type="text" class="form-control" name="full_name" required style="border: 1px solid #ddd; border-radius: 6px; padding: 10px;" placeholder="أدخل الاسم الكامل">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="font-weight: 600; color: #333; font-size: 14px;">رقم الهاتف *</label>
                            <input type="tel" class="form-control" name="phone" required style="border: 1px solid #ddd; border-radius: 6px; padding: 10px;" placeholder="05xxxxxxxx">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="font-weight: 600; color: #333; font-size: 14px;">رقم واتساب *</label>
                            <input type="tel" class="form-control" name="whatsapp" required style="border: 1px solid #ddd; border-radius: 6px; padding: 10px;" placeholder="05xxxxxxxx">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" style="font-weight: 600; color: #333; font-size: 14px;">ملاحظات إضافية</label>
                        <textarea class="form-control" name="notes" rows="3" style="border: 1px solid #ddd; border-radius: 6px; padding: 10px;" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                    </div>

                    <!-- شروط الخدمة -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms" style="color: #666; font-size: 14px;">
                                أوافق على <a href="terms-and-conditions.php" target="_blank" style="color: #ff9500;">الشروط والأحكام</a>
                            </label>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-6">
                            <a href="bus-booking.php" class="btn btn-outline-secondary w-100" style="border-radius: 6px; padding: 10px; font-weight: 600;">
                                ↩️ العودة
                            </a>
                        </div>
                        <div class="col-6">
                            <button type="submit" class="btn btn-primary w-100" style="background: #ff9500; border: none; border-radius: 6px; padding: 10px; font-weight: 600;">
                                ✅ تأكيد الحجز
                            </button>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>';

?>

<style>
/* تصميم بسيط وسريع للموبايل */
.form-control:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
}

@media (max-width: 768px) {
    .container {
        margin: 10px !important;
        padding: 0 10px !important;
    }

    .page-header, .trip-details, .booking-form {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .page-header h3 {
        font-size: 16px !important;
    }

    .trip-details h4 {
        font-size: 16px !important;
    }
}
</style>

<?php

?>

<script>
// معالج بسيط لإرسال النموذج
document.getElementById("bookingForm").addEventListener("submit", function(e) {
    const submitBtn = this.querySelector("button[type=submit]");
    submitBtn.innerHTML = '⏳ جاري التأكيد...';
    submitBtn.disabled = true;
});
</script>

<?php

include('footer.php');
?>

