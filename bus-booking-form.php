<?php
$Title_page = 'إكمال حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود البيانات
if (!isset($_GET['data'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = json_decode(urldecode($_GET['data']), true);
if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

// جلب تفاصيل الرحلة
$trip_id = $booking_data['trip_id'];
$stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
$stmt->execute([$trip_id]);
$trip = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$trip) {
    header('Location: bus-booking.php');
    exit;
}

echo '
<div class="container" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="page-header" style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h3 style="color: #333; margin: 0; text-align: center;">إكمال حجز الباص</h3>
                <p style="color: #666; margin: 5px 0 0 0; text-align: center; font-size: 14px;">أكمل بياناتك لتأكيد الحجز</p>
            </div>

            <!-- تفاصيل الرحلة -->
            <div class="trip-details" style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">
                    🚌 تفاصيل الرحلة
                </h4>

                <!-- التاريخ والوقت -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">📅 تاريخ السفر</div>';

// تحويل التاريخ إلى العربية
$date = strtotime($booking_data['travel_date']);
$arabic_days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
$arabic_months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$day_name = $arabic_days[date('w', $date)];
$day = date('d', $date);
$month = $arabic_months[date('n', $date)];
$year = date('Y', $date);
$arabic_date = $day_name . '، ' . $day . ' ' . $month . ' ' . $year;

echo '                            <div style="font-weight: 600; color: #333; font-size: 14px;">'.$arabic_date.'</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">🕐 وقت المغادرة</div>
                            <div style="font-weight: bold; color: #17a2b8; font-size: 16px;">'.date('h:i A', strtotime($trip['departure_time'])).'</div>
                        </div>
                    </div>
                </div>

                <!-- المحطات -->
                <div class="row mb-3">
                    <div class="col-md-6 mb-2">
                        <div style="background: #f0f8f0; border-left: 4px solid #28a745; padding: 12px; border-radius: 4px;">
                            <div style="font-size: 12px; color: #28a745; margin-bottom: 4px;">🟢 سفر من</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">'.$trip['from_city'].'</div>';

// إضافة محطة الانطلاق إذا كانت متوفرة
if (!empty($trip['from_station'])) {
    echo '                            <div style="color: #555; font-size: 13px; margin-bottom: 2px;">'.$trip['from_station'].'</div>';
}

// إضافة عنوان محطة الانطلاق إذا كان متوفراً
if (!empty($trip['departure_address'])) {
    echo '                            <div style="color: #666; font-size: 12px;">'.$trip['departure_address'].'</div>';
}

echo '                        </div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div style="background: #fef9f3; border-left: 4px solid #ff9500; padding: 12px; border-radius: 4px;">
                            <div style="font-size: 12px; color: #ff9500; margin-bottom: 4px;">🔴 وصول إلى</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">'.$trip['to_city'].'</div>';

// إضافة محطة الوصول إذا كانت متوفرة
if (!empty($trip['to_station'])) {
    echo '                            <div style="color: #555; font-size: 13px; margin-bottom: 2px;">'.$trip['to_station'].'</div>';
}

// إضافة عنوان محطة الوصول إذا كان متوفراً
if (!empty($trip['arrival_address'])) {
    echo '                            <div style="color: #666; font-size: 12px;">'.$trip['arrival_address'].'</div>';
}

echo '                        </div>
                    </div>
                </div>

                        <!-- معلومات إضافية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div style="background: #f8f9fa; border-radius: 6px; padding: 12px; text-align: center;">
                                    <div style="font-size: 13px; color: #666; margin-bottom: 4px;">عدد المسافرين</div>
                                    <div style="font-weight: bold; color: #333; font-size: 16px;">'.$booking_data['passenger_count'].' مسافر</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div style="background: #28a745; border-radius: 6px; padding: 12px; text-align: center; color: white;">
                                    <div style="font-size: 13px; margin-bottom: 4px; opacity: 0.9;">إجمالي المبلغ</div>
                                    <div style="font-weight: bold; font-size: 18px;">'.$booking_data['total_price'].' ريال</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج البيانات الشخصية -->
                    <form id="bookingForm" method="post" action="process-bus-booking.php">
                        <input type="hidden" name="trip_id" value="'.$trip_id.'">
                        <input type="hidden" name="travel_date" value="'.$booking_data['travel_date'].'">
                        <input type="hidden" name="seats_count" value="'.$booking_data['passenger_count'].'">
                        <input type="hidden" name="total_amount" value="'.$booking_data['total_price'].'">

                        <h4 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                            <span style="margin-left: 10px;">👤</span>
                            البيانات الشخصية
                        </h4>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">الاسم الكامل *</label>
                                <input type="text" class="form-control" name="full_name" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="أدخل الاسم الكامل">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="05xxxxxxxx">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم واتساب *</label>
                                <input type="tel" class="form-control" name="whatsapp" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="05xxxxxxxx">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label" style="font-weight: 600; color: #333;">ملاحظات إضافية</label>
                            <textarea class="form-control" name="notes" rows="3" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                        </div>

                        <!-- شروط الخدمة -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms" style="color: #666;">
                                    أوافق على <a href="terms-and-conditions.php" target="_blank" style="color: #ff9500;">الشروط والأحكام</a> و <a href="privacy-policy.php" target="_blank" style="color: #ff9500;">سياسة الخصوصية</a>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="bus-booking.php" class="btn btn-outline-secondary w-100" style="border-radius: 10px; padding: 12px; font-weight: bold;">
                                    العودة للبحث
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="submit" class="btn btn-primary w-100" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); border: none; border-radius: 10px; padding: 12px; font-weight: bold;">
                                    تأكيد الحجز
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>';

?>

<style>
/* تصميم مبسط وخفيف */
.trip-summary-card {
    transition: box-shadow 0.2s ease;
}

.trip-summary-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

/* تحسين النماذج */
.form-control:focus, .form-select:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
}

/* تحسين الأزرار */
.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    .booking-form-container {
        margin: 15px;
        padding: 20px !important;
    }

    .trip-summary-card {
        padding: 15px !important;
    }

    .trip-summary-card > div:nth-child(2) {
        flex-direction: column !important;
        gap: 10px !important;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .trip-summary-card h4 {
        font-size: 16px !important;
    }
}
</style>

<?php

?>

<script>
// معالج إرسال النموذج
document.getElementById("bookingForm").addEventListener("submit", function(e) {
    const submitBtn = this.querySelector("button[type=submit]");

    // تأثير التحميل البسيط
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري التأكيد...';
    submitBtn.disabled = true;
    submitBtn.style.background = "#6c757d";
});

// تحسين بسيط للنماذج
const formInputs = document.querySelectorAll("input[required]");
formInputs.forEach(input => {
    input.addEventListener("blur", function() {
        if (this.value.trim() === "") {
            this.style.borderColor = "#dc3545";
        } else {
            this.style.borderColor = "#28a745";
        }
    });
});
</script>

<?php

include('footer.php');
?>

