<?php
// ملف اختبار النظام
include_once('webset.php');

echo "<h2>اختبار نظام الصفحات المخصصة</h2>";

try {
    // اختبار الاتصال بقاعدة البيانات
    echo "<h3>1. اختبار قاعدة البيانات:</h3>";
    $stmt = $db->prepare("SHOW TABLES LIKE 'custom_bus_pages'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ جدول custom_bus_pages موجود<br>";
        
        // عرض الصفحات المخصصة
        $stmt = $db->prepare("SELECT * FROM custom_bus_pages");
        $stmt->execute();
        $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>2. الصفحات المخصصة الموجودة:</h3>";
        if (empty($pages)) {
            echo "❌ لا توجد صفحات مخصصة<br>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>اسم الصفحة</th><th>الرابط</th><th>من</th><th>إلى</th><th>الحالة</th></tr>";
            foreach ($pages as $page) {
                $status = $page['is_active'] ? '✅ نشط' : '❌ غير نشط';
                echo "<tr>";
                echo "<td>{$page['id']}</td>";
                echo "<td>{$page['page_name']}</td>";
                echo "<td><a href='{$Site_URL}/{$page['page_url']}' target='_blank'>{$page['page_url']}</a></td>";
                echo "<td>{$page['from_city']}</td>";
                echo "<td>{$page['to_city']}</td>";
                echo "<td>{$status}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "❌ جدول custom_bus_pages غير موجود<br>";
        echo "يرجى تنفيذ ملف create_custom_pages_table.sql<br>";
    }
    
    // اختبار جدول bus_trips
    echo "<h3>3. اختبار جدول الرحلات:</h3>";
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM bus_trips");
    $stmt->execute();
    $trips_count = $stmt->fetchColumn();
    echo "عدد الرحلات في قاعدة البيانات: {$trips_count}<br>";
    
    if ($trips_count > 0) {
        echo "✅ يوجد رحلات في قاعدة البيانات<br>";
        
        // عرض بعض المدن المتاحة
        $stmt = $db->prepare("SELECT DISTINCT from_city, to_city FROM bus_trips LIMIT 5");
        $stmt->execute();
        $sample_routes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>عينة من الرحلات المتاحة:</h4>";
        foreach ($sample_routes as $route) {
            echo "- من {$route['from_city']} إلى {$route['to_city']}<br>";
        }
    } else {
        echo "❌ لا توجد رحلات في قاعدة البيانات<br>";
    }
    
    // اختبار ملف .htaccess
    echo "<h3>4. اختبار إعادة التوجيه:</h3>";
    if (file_exists('.htaccess')) {
        $htaccess_content = file_get_contents('.htaccess');
        if (strpos($htaccess_content, 'Custom Bus Pages Routes') !== false) {
            echo "✅ قواعد إعادة التوجيه موجودة في .htaccess<br>";
        } else {
            echo "❌ قواعد إعادة التوجيه غير موجودة في .htaccess<br>";
        }
    } else {
        echo "❌ ملف .htaccess غير موجود<br>";
    }
    
    // اختبار الملفات المطلوبة
    echo "<h3>5. اختبار الملفات:</h3>";
    $required_files = [
        'custom-bus-page.php',
        'add-custom-page.php',
        'toggle-page-status.php',
        'bus-api.php'
    ];
    
    foreach ($required_files as $file) {
        if (file_exists($file)) {
            echo "✅ {$file} موجود<br>";
        } else {
            echo "❌ {$file} غير موجود<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>الروابط المهمة:</h3>";
echo "<ul>";
echo "<li><a href='add-custom-page.php'>إدارة الصفحات المخصصة</a></li>";
echo "<li><a href='buses-mecca-to-riyadh'>مثال: صفحة مكة → الرياض</a></li>";
echo "<li><a href='buses-riyadh-to-mecca'>مثال: صفحة الرياض → مكة</a></li>";
echo "<li><a href='bus-booking.php'>الصفحة الأساسية للحجز</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، فالنظام جاهز للاستخدام!</p>";
?>