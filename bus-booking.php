<?php
$Title_page = 'حجز باصات من والى مكة المكرمة';
include_once('webset.php');

// جلب الصفحات المخصصة النشطة
$custom_pages = [];
try {
    $stmt = $db->prepare("SELECT cp.page_name, cp.page_url, cp.page_title, bt.from_city, bt.to_city 
                         FROM custom_bus_pages cp 
                         JOIN bus_trips bt ON cp.trip_id = bt.id 
                         WHERE cp.is_active = 1 
                         ORDER BY cp.created_at DESC");
    $stmt->execute();
    $custom_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // في حالة الخطأ، نتجاهل ونكمل بدون عرض الصفحات المخصصة
}

include_once('header.php');
include_once('navbar.php');

// إضافة Font Awesome للأيقونات العصرية
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">';

// إضافة jQuery UI للتاريخ العربي
echo '<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">';
echo '<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>';



// المدن يتم جلبها ديناميكياً من جدول bus_trips عبر API

echo '
<section class="bus-booking-hero" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); min-height: 100vh; position: relative; overflow: hidden;  padding-top: 40px;">
    <div class="container-fluid h-100">
        <div class="row align-items-center h-100">
            <!-- النص والعنوان مع صورة الباص -->
            <div class="col-lg-7 hero-text-section">
                <div class="hero-content text-white">
                  
                    <h1 class="hero-title" style="font-size: 3.5rem; font-weight: bold; line-height: 1.2; margin-bottom: 20px;">باصات من والى مكة<br><span style="color: #fff3e0;">يومياً</span></h1>
                    <div class="bus-image-container" style="position: relative; margin-top: 40px;">
                        <img src="'.$Site_URL.'/img/bus-image-ar.png" alt="Bus" class="bus-image" style="width: 100%;  height: auto; border-radius: 15px; ">
                    </div>
                </div>
            </div>

            <!-- نموذج الحجز -->
            <div class="col-lg-5 booking-section">
                <div class="booking-form-container" style="padding: 0px;">
                    <div class="booking-form-card" style="background: white; border-radius: 20px; padding: 30px; box-shadow: 0 20px 60px rgba(0,0,0,0.1);">
                        <!-- عنوان النموذج -->
                        <div class="form-header mb-4">
                            <div class="text-center">
                                <h4 style="color: #333; font-weight: bold; margin-bottom: 10px;">ابحث عن رحلتك</h4>
                                <p style="color: #666; font-size: 14px; margin-bottom: 10px;">اختر مدينة السفر والوصول للعثور على أفضل الرحلات</p>
                               
                            </div>
                        </div>

                        <form id="busSearchForm">
                            <!-- checkbox ذهاب فقط في أول النموذج -->
                            <div class="row mb-2">
                                <div class="col-12">
                                    <div class="form-check d-flex align-items-center" style="background: #fff; border: 1px solid #ddd; border-radius: 6px; padding: 8px 12px;">
                                        <input class="form-check-input" type="checkbox" id="oneWayOnly" name="one_way_only" value="1" checked style="margin: 0; margin-left: 8px;">
                                        <label class="form-check-label" for="oneWayOnly" style="font-weight: normal; color: #333; font-size: 14px; margin: 0; cursor: pointer;">
                                            ذهاب فقط
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- مدينة السفر -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label" style="font-weight: 600; color: #28a745; margin-bottom: 8px;">
                                        <i class="fas fa-map-marker-alt" style="margin-left: 8px; font-size: 18px;"></i>
                                        <span>هتسافر منين</span>
                                    </label>
                                    <select class="form-select" id="fromCity" name="from_city" style="border: 2px solid #28a745; border-radius: 10px; padding: 12px; font-size: 16px;">
                                        <option value="">اختر مدينة السفر</option>
                                    </select>
                                </div>
                            </div>

                            <!-- محطة السفر -->
                            <div class="row mb-3" id="fromStationRow" style="display: none;">
                                <div class="col-12">
                                
                                    <input type="hidden" id="fromStation" name="from_station" value="">
                                </div>
                            </div>

                            <!-- مدينة الوصول -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label" style="font-weight: 600; color: #ff9500; margin-bottom: 8px;">
                                        <i class="fas fa-flag-checkered" style="margin-left: 8px; font-size: 18px;"></i>
                                        <span>هتوصل فين</span>
                                    </label>
                                    <select class="form-select" id="toCity" name="to_city" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px;" disabled>
                                        <option value="">اختر مدينة الوصول</option>
                                    </select>
                                </div>
                            </div>

                            <!-- محطة الوصول -->
                            <div class="row mb-3" id="toStationRow" style="display: none;">
                                <div class="col-12">
                                  <div class="station-display" style="display: flex;align-items: center;gap: 12px;">
                                       <div style="width: 10px; height: 10px; background: #28a745; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                                        <span style="font-weight: 600; color: #333; font-size: 16px;">
                                            محطة السفر المتاحة - <span id="fromStationInfo" style="color: #28a745;">سيتم عرض محطة السفر هنا</span>
                                        </span>
                                    </div>
                                    <div class="station-display" style="display: flex;align-items: center;gap: 12px;">
<div style="width: 10px; height: 10px; background: #ff9500; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>                                        <span style="font-weight: 600; color: #333; font-size: 16px;">
                                            محطة الوصول المتاحة - <span id="toStationInfo" style="color: #ff9500;">سيتم عرض محطة الوصول هنا</span>
                                        </span>
                                    </div>
                                    <input type="hidden" id="toStation" name="to_station" value="">
                                </div>
                            </div>

                            <!-- تاريخ السفر وعدد المسافرين -->
                            <div class="row mb-4">
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        <i class="fas fa-calendar-alt" style="margin-left: 8px; color: #ffc107; font-size: 16px;"></i>
                                        <span>تاريخ السفر</span>
                                    </label>
                                    <input type="text" class="form-control" id="travelDate" name="travel_date" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px; font-size: 16px; direction: rtl; text-align: right;" placeholder="اختر تاريخ السفر" readonly>
                                </div>
                                <div class="col-6">
                                    <label class="form-label" style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                        <i class="fas fa-users" style="margin-left: 8px; color: #17a2b8; font-size: 16px;"></i>
                                        <span>عدد المسافرين</span>
                                    </label>
                                    <div class="passenger-counter" style="display: flex; align-items: center; border: 2px solid #e9ecef; border-radius: 10px; padding: 8px;">
                                        <button type="button" class="btn-counter" id="decreaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">-</button>
                                        <input type="number" class="form-control text-center" id="passengerCount" name="passenger_count" value="1" min="1" max="10" style="border: none; font-size: 18px; font-weight: bold; background: transparent;" readonly>
                                        <button type="button" class="btn-counter" id="increaseBtn" style="background: #f8f9fa; border: none; width: 40px; height: 40px; border-radius: 8px; font-size: 18px; font-weight: bold; color: #666;">+</button>
                                    </div>
                                    <small class="text-muted" style="font-size: 12px;">يُنصح بحجز مقعد للأطفال مع ولي الأمر</small>
                                </div>
                            </div>

                            <!-- زر البحث -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-search w-100" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); color: white; border: none; border-radius: 12px; padding: 15px; font-size: 18px; font-weight: bold; transition: all 0.3s;">
                                        <i class="fas fa-search" style="margin-left: 8px;"></i>
                                        <span>أعرض الرحلات</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم عرض النتائج -->
<section id="searchResults" class="search-results-section" style="display: none; padding: 60px 0; background: #f8f9fa;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center mb-5" style="color: #333; font-weight: bold;">الرحلات المتاحة</h2>
                <div id="tripsContainer" class="trips-container">
                    <!-- سيتم عرض الرحلات هنا -->
                </div>
            </div>
        </div>
    </div>
</section>';

// عرض قسم الصفحات المخصصة إذا كانت متوفرة
if (!empty($custom_pages)) {
    echo '
<!-- قسم الصفحات المخصصة -->
<section class="custom-pages-section" style="padding: 40px 0; background: #ffffff;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 style="color: #333; font-weight: bold; margin-bottom: 20px; text-align: center;">
                    <i class="fas fa-route" style="color: #ff9500; margin-left: 10px;"></i>
                    رحلات مخصصة متاحة
                </h3>
                <div class="custom-pages-list" style="max-width: 800px; margin: 0 auto;">
    ';
    
    foreach ($custom_pages as $page) {
        $page_url = $Site_URL . '/custom-bus-page.php?page=' . urlencode($page['page_url']);
        echo '
                    <div class="custom-page-item" style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; transition: all 0.3s ease;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <h5 style="margin: 0 0 5px 0; color: #333; font-weight: 600;">
                                    <i class="fas fa-bus" style="color: #28a745; margin-left: 8px; font-size: 14px;"></i>
                                    ' . htmlspecialchars($page['page_name']) . '
                                </h5>
                                <p style="margin: 0; color: #666; font-size: 14px;">
                                    <span style="color: #28a745; font-weight: 500;">' . htmlspecialchars($page['from_city']) . '</span>
                                    <i class="fas fa-arrow-left" style="margin: 0 8px; color: #ff9500; font-size: 12px;"></i>
                                    <span style="color: #ff9500; font-weight: 500;">' . htmlspecialchars($page['to_city']) . '</span>
                                </p>
                            </div>
                            <div>
                                <a href="' . $page_url . '" class="btn" style="background: #ff9500; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; text-decoration: none; transition: all 0.3s ease;">
                                    <i class="fas fa-external-link-alt" style="margin-left: 5px; font-size: 12px;"></i>
                                    عرض الرحلة
                                </a>
                            </div>
                        </div>
                    </div>
        ';
    }
    
    echo '
                </div>
            </div>
        </div>
    </div>
</section>
    ';
}

echo '
<style>
.bus-booking-hero {
    position: relative;
}

.bus-booking-hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

/* تحسينات للنموذج الجديد */
.form-select:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
    transform: translateY(-1px);
}

.form-select:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px !important;
}

.form-select {
    transition: all 0.3s ease;
}

.form-select:hover:not(:disabled) {
    border-color: #ff9500;
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.15);
}

.station-display {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.station-display:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.station-display i {
    transition: all 0.3s ease;
}

.station-display:hover i {
    transform: scale(1.1);
}

/* تأثيرات الأيقونات */
.form-label i {
    transition: all 0.3s ease;
}

.form-label:hover i {
    transform: scale(1.1);
}

.btn-search i {
    transition: all 0.3s ease;
}

.btn-search:hover i {
    transform: rotate(360deg);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تصميم كارت الرحلات المبسط */
.trip-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.trip-card:hover {
    border-color: #ff9500;
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
}

/* أنماط إضافية للكارت الجديد */
.trip-card .date-time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.trip-card .station-box {
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border: 1px solid;
}

.trip-card .departure-box {
    background: #e8f5e8;
    border-color: #28a745;
}

.trip-card .arrival-box {
    background: #fff8f0;
    border-color: #ff9500;
}

.price-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.price-display {
    font-size: 20px;
    font-weight: bold;
    color: #28a745;
}

.book-btn {
    background: #ff9500;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 25px;
    font-weight: bold;
    transition: background 0.2s ease;
}

.book-btn:hover {
    background: #ff7b00;
    color: white;
}

/* تصميم قسم الصفحات المخصصة */
.custom-page-item:hover {
    border-color: #ff9500;
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.15);
    transform: translateY(-2px);
}

.custom-page-item .btn:hover {
    background: #ff7b00 !important;
    transform: scale(1.05);
}

.custom-page-item i {
    transition: all 0.3s ease;
}

.custom-page-item:hover i.fa-bus {
    transform: scale(1.1);
}

.custom-page-item:hover i.fa-arrow-left {
    transform: translateX(-3px);
}

@media (max-width: 768px) {
    .custom-page-item {
        padding: 12px !important;
    }
    
    .custom-page-item > div {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px;
    }
    
    .custom-page-item .btn {
        align-self: flex-end;
    }
}

/* تصميم checkbox ذهاب فقط */
.form-check {
    transition: all 0.2s ease;
}

.form-check:hover {
    border-color: #bbb !important;
}

.form-check-input {
    border: 1px solid #ccc;
    border-radius: 3px;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.form-check-label {
    user-select: none;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .booking-form-card {
        margin: 5px;
        padding: 20px !important;
    }

    .trip-details {
        grid-template-columns: 1fr 1fr;
    }

    /* تحسين التخطيط للموبايل */
    .col-6 .form-label {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .col-6 .form-select,
    .col-6 .form-control {
        padding: 8px;
        font-size: 14px;
    }

    .passenger-counter {
        padding: 4px !important;
    }

    .btn-counter {
        width: 30px !important;
        height: 30px !important;
        font-size: 14px !important;
    }

    .passenger-counter input {
        font-size: 14px !important;
    }
}

/* تحسين مظهر jQuery UI Datepicker */
.ui-datepicker {
    font-family: Arial, "Segoe UI", sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
    border-radius: 10px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
    border: none !important;
}

.ui-datepicker-header {
    background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px 10px 0 0 !important;
    padding: 10px !important;
}

.ui-datepicker-title {
    color: white !important;
    font-weight: bold !important;
}

.ui-datepicker-prev, .ui-datepicker-next {
    background: rgba(255,255,255,0.2) !important;
    border: none !important;
    border-radius: 5px !important;
}

.ui-datepicker-prev:hover, .ui-datepicker-next:hover {
    background: rgba(255,255,255,0.3) !important;
}

.ui-datepicker td {
    text-align: center !important;
    padding: 2px !important;
}

.ui-datepicker td a {
    padding: 8px !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    color: #333 !important;
}

.ui-datepicker td a:hover {
    background: #ff9500 !important;
    color: white !important;
}

.ui-datepicker .ui-state-active {
    background: #ff7b00 !important;
    color: white !important;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // إعداد jQuery UI Datepicker العربي
    $.datepicker.regional["ar"] = {
        closeText: "إغلاق",
        prevText: "السابق",
        nextText: "التالي",
        currentText: "اليوم",
        monthNames: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
        monthNamesShort: ["1", "2", "3", "4", "5", "6",
            "7", "8", "9", "10", "11", "12"],
        dayNames: ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"],
        dayNamesShort: ["أحد", "اثن", "ثلاث", "أربع", "خميس", "جمع", "سبت"],
        dayNamesMin: ["ح", "ن", "ث", "ر", "خ", "ج", "س"],
        weekHeader: "أسبوع",
        dateFormat: "yy-mm-dd",
        firstDay: 0,
        isRTL: true,
        showMonthAfterYear: false,
        yearSuffix: ""
    };
    $.datepicker.setDefaults($.datepicker.regional["ar"]);

    // تطبيق datepicker على مربع التاريخ
    $("#travelDate").datepicker({
        dateFormat: "yy-mm-dd",
        minDate: 0,
        changeMonth: true,
        changeYear: true,
        showAnim: "slideDown"
    });

    // تعيين التاريخ الحالي كقيمة افتراضية
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const formattedDate = year + "-" + month + "-" + day;
    $("#travelDate").val(formattedDate);

    // عناصر النموذج الجديد
    const fromCitySelect = document.getElementById("fromCity");
    const toCitySelect = document.getElementById("toCity");
    const fromStationInfo = document.getElementById("fromStationInfo");
    const toStationInfo = document.getElementById("toStationInfo");
    const fromStationInput = document.getElementById("fromStation");
    const toStationInput = document.getElementById("toStation");
    const fromStationRow = document.getElementById("fromStationRow");
    const toStationRow = document.getElementById("toStationRow");
    const decreaseBtn = document.getElementById("decreaseBtn");
    const increaseBtn = document.getElementById("increaseBtn");
    const passengerCount = document.getElementById("passengerCount");
    const busSearchForm = document.getElementById("busSearchForm");

    // تحميل المدن عند تحميل الصفحة
    console.log("تحميل المدن...");
    loadAllCities();

    // دالة تحميل جميع المدن المتاحة للسفر
    function loadAllCities() {
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_all_cities"
            })
        })
        .then(response => response.json())
        .then(data => {
            fromCitySelect.innerHTML = "<option value=\\"\\">اختر مدينة السفر</option>";

            if (data.success && data.cities) {
                data.cities.forEach(city => {
                    const option = document.createElement("option");
                    option.value = city;
                    option.textContent = city;
                    fromCitySelect.appendChild(option);
                });
                console.log("تم تحميل " + data.cities.length + " مدينة");
            }
        })
        .catch(error => {
            console.error("Error loading cities:", error);
            fromCitySelect.innerHTML = "<option value=\\"\\">خطأ في تحميل المدن</option>";
        });
    }

    // دالة تحميل مدن الوصول بناءً على مدينة السفر
    function loadDestinationCities(fromCity) {
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_destination_cities",
                from_city: fromCity
            })
        })
        .then(response => response.json())
        .then(data => {
            toCitySelect.innerHTML = "<option value=\\"\\">اختر مدينة الوصول</option>";
            toCitySelect.disabled = false;
            toCitySelect.style.borderColor = "#ff9500";

            if (data.success && data.cities) {
                data.cities.forEach(city => {
                    const option = document.createElement("option");
                    option.value = city;
                    option.textContent = city;
                    toCitySelect.appendChild(option);
                });
                console.log("تم تحميل " + data.cities.length + " مدينة وصول");
            }
        })
        .catch(error => {
            console.error("Error loading destination cities:", error);
        });
    }

    // دالة تحميل وعرض محطة السفر
    function loadAndDisplayDepartureStation(fromCity, toCity) {
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_departure_stations",
                from_city: fromCity,
                to_city: toCity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.stations && data.stations.length > 0) {
                // عرض أول محطة سفر
                const firstDepartureStation = data.stations[0];
                fromStationInfo.textContent = firstDepartureStation;
                fromStationInput.value = firstDepartureStation;

                // إظهار محطة السفر
                fromStationRow.style.display = "block";
                fromStationRow.style.animation = "fadeIn 0.5s ease-in";

                console.log("تم تحميل محطة السفر:", firstDepartureStation);

                // تحميل محطة الوصول
                loadAndDisplayArrivalStation(fromCity, toCity, firstDepartureStation);
            }
        })
        .catch(error => {
            console.error("Error loading departure station:", error);
        });
    }

    // دالة تحميل وعرض محطة الوصول
    function loadAndDisplayArrivalStation(fromCity, toCity, fromStation) {
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_arrival_stations",
                from_city: fromCity,
                to_city: toCity,
                from_station: fromStation
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.stations && data.stations.length > 0) {
                // عرض أول محطة وصول
                const firstArrivalStation = data.stations[0];
                toStationInfo.textContent = firstArrivalStation;
                toStationInput.value = firstArrivalStation;

                // إظهار محطة الوصول
                toStationRow.style.display = "block";
                toStationRow.style.animation = "fadeIn 0.5s ease-in";

                console.log("تم تحميل محطة الوصول:", firstArrivalStation);
            }
        })
        .catch(error => {
            console.error("Error loading arrival station:", error);
        });
    }



    // Event listeners للحقول الجديدة

    // عند اختيار مدينة السفر
    fromCitySelect.addEventListener("change", function() {
        const selectedCity = this.value;

        // إعادة تعيين الحقول التابعة
        toCitySelect.innerHTML = "<option value=\\"\\">اختر مدينة الوصول</option>";
        toCitySelect.disabled = true;
        fromStationRow.style.display = "none";
        toStationRow.style.display = "none";
        fromStationInput.value = "";
        toStationInput.value = "";

        if (selectedCity) {
            toCitySelect.innerHTML = "<option value=\\"\\">جاري التحميل...</option>";
            loadDestinationCities(selectedCity);
        }
    });

    // عند اختيار مدينة الوصول
    toCitySelect.addEventListener("change", function() {
        const selectedToCity = this.value;
        const selectedFromCity = fromCitySelect.value;

        // إخفاء المحطات
        fromStationRow.style.display = "none";
        toStationRow.style.display = "none";
        fromStationInput.value = "";
        toStationInput.value = "";

        if (selectedToCity && selectedFromCity) {
            // تحميل وعرض المحطات
            loadAndDisplayDepartureStation(selectedFromCity, selectedToCity);
        }
    });

    // عداد المسافرين
    decreaseBtn.addEventListener("click", function() {
        let count = parseInt(passengerCount.value);
        if (count > 1) {
            passengerCount.value = count - 1;
        }
    });

    increaseBtn.addEventListener("click", function() {
        let count = parseInt(passengerCount.value);
        if (count < 10) {
            passengerCount.value = count + 1;
        }
    });

    // معالج البحث
    busSearchForm.addEventListener("submit", function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const searchData = {
            action: "search_new_trips",
            from_city: formData.get("from_city"),
            to_city: formData.get("to_city"),
            from_station: formData.get("from_station"),
            to_station: formData.get("to_station"),
            travel_date: formData.get("travel_date"),
            passenger_count: formData.get("passenger_count")
        };

        // التحقق من البيانات المطلوبة
        if (!searchData.from_city) {
            alert("يرجى اختيار مدينة السفر");
            return;
        }

        if (!searchData.to_city) {
            alert("يرجى اختيار مدينة الوصول");
            return;
        }

        if (!searchData.from_station) {
            alert("يرجى انتظار تحميل محطات السفر");
            return;
        }

        if (!searchData.to_station) {
            alert("يرجى انتظار تحميل محطات الوصول");
            return;
        }

        if (!searchData.travel_date) {
            alert("يرجى اختيار تاريخ السفر");
            return;
        }

        console.log("بيانات البحث:", searchData);

        // عرض مؤشر التحميل
        const searchBtn = this.querySelector(".btn-search");
        const originalText = searchBtn.innerHTML;
        searchBtn.innerHTML = "<span class=\\"spinner-border spinner-border-sm me-2\\"></span>جاري البحث...";
        searchBtn.disabled = true;

        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(searchData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTrips(data.trips, searchData);
                document.getElementById("searchResults").style.display = "block";
                document.getElementById("searchResults").scrollIntoView({ behavior: "smooth" });
            } else {
                alert(data.message || "لم يتم العثور على رحلات متاحة");
            }
        })
        .catch(error => {
            console.error("Error searching trips:", error);
            alert("حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.");
        })
        .finally(() => {
            searchBtn.innerHTML = originalText;
            searchBtn.disabled = false;
        });
    });

    // عرض الرحلات
    function displayTrips(trips, searchData) {
        const container = document.getElementById("tripsContainer");

        if (!trips || trips.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-bus" style="font-size: 4rem; color: #ff9500;"></i>
                    </div>
                    <h4>لا توجد رحلات متاحة</h4>
                    <p class="text-muted">لم يتم العثور على رحلات متاحة للتاريخ والمسار المحدد</p>
                </div>
            `;
            return;
        }

        let html = "";
        trips.forEach((trip, index) => {
            const totalPrice = (parseFloat(trip.seat_price) * parseInt(searchData.passenger_count)).toFixed(2);

            html += `
                <div class="trip-card">
                    <!-- 1. تاريخ الرحلة وموعد المغادرة على نفس السطر -->
                    <div style="display: flex; justify-content: space-between; align-items: center; background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-calendar-alt" style="color: #ff9500; font-size: 16px;"></i>
                            <span style="font-weight: 600; color: #333; font-size: 15px;">${formatDate(searchData.travel_date)}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-clock" style="color: #17a2b8; font-size: 16px;"></i>
                            <span style="font-weight: bold; color: #17a2b8; font-size: 26px;">${formatTime(trip.departure_time)}</span>
                        </div>
                    </div>

                    <!-- 2. مربع مدينة السفر مع محطة الانطلاق -->
                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 8px; padding: 12px; margin-bottom: 10px;">
                                          <div style="display: flex; align-items: flex-start; gap: 8px;">
                          سفر من  <div style="width: 10px; height: 10px; background: #28a745; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: bold; color: #28a745; font-size: 16px; margin-bottom: 5px;">${trip.from_city}</div>
                `;

                // إضافة عنوان محطة الانطلاق إذا كان متوفراً
                if (trip.departure_address) {
                    html += `
                                <div style="color: #000000;font-size: 14px;line-height: 1.4;border-radius: 4px;margin-top: 5px;">${trip.departure_address}</div>
                    `;
                }

                html += `
                            </div>
                        </div>
                    </div>

                    <!-- 3. مربع مدينة الوصول مع محطة الوصول -->
                     <div style="background: #fff8f0; border: 1px solid #ff9500; border-radius: 8px; padding: 12px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                        وصول الى     <div style="width: 10px; height: 10px; background: #ff9500; border-radius: 50%; flex-shrink: 0; margin-top: 4px;"></div>
                            <div style="flex: 1;">
                              <div style="font-weight: bold; color: #ff9500; font-size: 16px; margin-bottom: 5px;">${trip.to_city}</div>
                `;

                // إضافة عنوان محطة الوصول إذا كان متوفراً
                if (trip.arrival_address) {
                    html += `
                                <div style="color: #000000;font-size: 14px;line-height: 1.4;border-radius: 4px;margin-top: 5px;">${trip.arrival_address}</div>
                    `;
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;

                // إضافة رابط الموقع إذا كان متوفراً
                if (trip.departure_location) {
                    html += `
                    <div style="margin: 10px 0; text-align: center;">
                        <a href="${trip.departure_location}" target="_blank" style="color: #ff9500; text-decoration: none; font-size: 13px; padding: 5px 10px; border: 1px solid #ff9500; border-radius: 5px; display: inline-block;">
                            <i class="fa fa-map-marker" style="margin-left: 5px;"></i>
                            عرض الموقع
                        </a>
                    </div>
                    `;
                }

                html += `
                    <!-- قسم المسافرين والسعر المبسط -->
                    <div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <!-- عدد المسافرين -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333; font-size: 14px;">عدد المسافرين:</span>
                            <div style="display: flex; align-items: center; background: white; border-radius: 5px; border: 1px solid #ddd;">
                                <button type="button" onclick="updatePassengerCount(${index}, -1)" style="background: none; border: none; width: 30px; height: 30px; font-size: 14px; font-weight: bold; color: #666; cursor: pointer;">-</button>
                                <input type="number" id="passenger-count-${index}" value="${searchData.passenger_count}" min="1" max="10" style="border: none; width: 40px; text-align: center; font-size: 14px; font-weight: bold; background: transparent;" readonly>
                                <button type="button" onclick="updatePassengerCount(${index}, 1)" style="background: none; border: none; width: 30px; height: 30px; font-size: 14px; font-weight: bold; color: #666; cursor: pointer;">+</button>
                            </div>
                        </div>

                        <!-- سعر المقعد -->
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: bold; color: #333; font-size: 14px;">سعر المقعد:</span>
                            <span style="font-size: 16px; font-weight: bold; color: #28a745;">${trip.seat_price} ريال</span>
                        </div>
                    </div>

                    <div class="price-section">
                        <div>
                            <div class="price-display" id="total-price-${index}">${totalPrice} ريال</div>
                            <div style="font-size: 14px; color: #666;" id="passenger-text-${index}">لـ ${searchData.passenger_count} مسافر</div>
                        </div>
                        <button class="book-btn" id="book-btn-${index}" onclick="bookTrip(${trip.id}, \'${searchData.travel_date}\', ${searchData.passenger_count}, ${totalPrice})">
                            احجز الآن
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;

        // حفظ بيانات الرحلات للاستخدام في تحديث الأسعار
        window.currentTripsData = trips;
    }

    // تنسيق الوقت
    function formatTime(timeString) {
        const time = new Date("2000-01-01 " + timeString);
        return time.toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true
        });
    }

    // تنسيق التاريخ بالعربي مع التاريخ الميلادي
    function formatDate(dateString) {
        const date = new Date(dateString);

        // أسماء الأيام بالعربية
        const arabicDays = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];

        // أسماء الأشهر الميلادية بالعربية
        const arabicMonths = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ];

        // الحصول على اليوم والتاريخ
        const dayName = arabicDays[date.getDay()];
        const day = date.getDate();
        const month = arabicMonths[date.getMonth()];
        const year = date.getFullYear();

        // تنسيق التاريخ: اليوم، اليوم الشهر السنة
        return `${dayName}، ${day} ${month} ${year}`;
    }


});

// دالة حجز الرحلة
function bookTrip(tripId, travelDate, passengerCount, totalPrice) {
    // إنشاء نموذج الحجز
    const bookingData = {
        trip_id: tripId,
        travel_date: travelDate,
        passenger_count: passengerCount,
        total_price: totalPrice
    };

    // تحويل إلى صفحة الحجز
    const encodedData = encodeURIComponent(JSON.stringify(bookingData));
    window.location.href = `bus-booking-form.php?data=${encodedData}`;
}



// دالة تحديث عدد المسافرين
function updatePassengerCount(tripIndex, change) {
    const passengerInput = document.getElementById(`passenger-count-${tripIndex}`);
    const totalPriceElement = document.getElementById(`total-price-${tripIndex}`);
    const passengerTextElement = document.getElementById(`passenger-text-${tripIndex}`);
    const bookButton = document.getElementById(`book-btn-${tripIndex}`);

    let currentCount = parseInt(passengerInput.value);
    let newCount = currentCount + change;

    // التحقق من الحدود
    if (newCount < 1) newCount = 1;
    if (newCount > 10) newCount = 10;

    // تحديث العدد
    passengerInput.value = newCount;

    // الحصول على سعر المقعد الواحد من البيانات المخزنة
    const tripData = window.currentTripsData[tripIndex];
    const seatPrice = parseFloat(tripData.seat_price);
    const newTotalPrice = (seatPrice * newCount).toFixed(2);

    // تحديث السعر الإجمالي
    totalPriceElement.textContent = newTotalPrice + " ريال";

    // تحديث نص المسافرين
    passengerTextElement.textContent = `لـ ${newCount} مسافر`;

    // تحديث زر الحجز
    const travelDate = document.getElementById("travelDate").value;
    bookButton.setAttribute("onclick", "bookTrip(" + tripData.id + ", \'" + travelDate + "\', " + newCount + ", " + newTotalPrice + ")");

    // تأثير بصري للتغيير
    totalPriceElement.style.transform = "scale(1.1)";
    totalPriceElement.style.color = "#ff9500";
    setTimeout(function() {
        totalPriceElement.style.transform = "scale(1)";
        totalPriceElement.style.color = "#ff9500";
    }, 200);
}
</script>
';

include('footer.php');
?>
