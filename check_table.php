<?php
include_once('config.php');

try {
    // التحقق من وجود الجدول
    $stmt = $db->prepare("SHOW TABLES LIKE 'custom_bus_pages'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ جدول custom_bus_pages موجود في قاعدة البيانات<br><br>";
        
        // عرض بنية الجدول
        echo "<h3>بنية الجدول:</h3>";
        $stmt = $db->prepare("DESCRIBE custom_bus_pages");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr style='background: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عرض البيانات الموجودة
        $stmt = $db->prepare("SELECT * FROM custom_bus_pages");
        $stmt->execute();
        $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>البيانات الموجودة في الجدول:</h3>";
        echo "<p>عدد الصفحات: " . count($pages) . "</p>";
        
        if (!empty($pages)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>اسم الصفحة</th><th>الرابط</th><th>من</th><th>إلى</th><th>الحالة</th><th>تاريخ الإنشاء</th>";
            echo "</tr>";
            
            foreach ($pages as $page) {
                $status = $page['is_active'] ? '✅ نشط' : '❌ غير نشط';
                echo "<tr>";
                echo "<td>{$page['id']}</td>";
                echo "<td>{$page['page_name']}</td>";
                echo "<td>{$page['page_url']}</td>";
                echo "<td>{$page['from_city']}</td>";
                echo "<td>{$page['to_city']}</td>";
                echo "<td>{$status}</td>";
                echo "<td>{$page['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<hr>";
            echo "<h3>اختبار الروابط:</h3>";
            echo "<ul>";
            foreach ($pages as $page) {
                echo "<li><a href='custom-bus-page.php?page={$page['page_url']}' target='_blank'>{$page['page_title']}</a></li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "❌ جدول custom_bus_pages غير موجود في قاعدة البيانات";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>