<?php
$Title_page = 'تأكيد الحجز';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود بيانات الحجز
if (!isset($_SESSION['booking_success']) && !isset($_GET['ref'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = null;

// إذا كانت البيانات في الجلسة
if (isset($_SESSION['booking_success'])) {
    $booking_data = $_SESSION['booking_success'];
    unset($_SESSION['booking_success']); // حذف البيانات من الجلسة
}
// أو جلب البيانات من قاعدة البيانات باستخدام الرقم المرجعي
elseif (isset($_GET['ref'])) {
    $booking_ref = $_GET['ref'];

    $stmt = $db->prepare("SELECT b.*, t.* FROM bus_bookings b
                         JOIN bus_trips t ON b.trip_id = t.id
                         WHERE b.booking_reference = ?");
    $stmt->execute([$booking_ref]);
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($booking) {
        $booking_data = [
            'booking_id' => $booking['id'],
            'booking_reference' => $booking['booking_reference'],
            'trip' => $booking,
            'travel_date' => $booking['travel_date'],
            'seats_count' => $booking['seats_count'],
            'total_amount' => $booking['total_amount'],
            'passenger_name' => $booking['passenger_name'],
            'phone' => $booking['passenger_phone'],
            'whatsapp' => $booking['passenger_whatsapp']
        ];
    }
}

if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

echo '<div class="container" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- رسالة النجاح -->
            <div class="success-card" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 20px; text-align: center;">
                <div style="font-size: 48px; color: #28a745; margin-bottom: 15px;">✓</div>
                <h2 style="color: #155724; margin-bottom: 10px; font-size: 24px;">تم تأكيد حجزك بنجاح!</h2>
                <p style="color: #155724; margin: 0; font-size: 16px;">شكراً لك، تم حجز رحلتك بنجاح</p>
            </div>

            <!-- تفاصيل الحجز -->
            <div class="booking-details" style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <div style="border-bottom: 1px solid #e9ecef; padding-bottom: 15px; margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px; font-size: 20px;">تفاصيل الحجز</h4>
                    <div style="background: #ff9500; color: white; padding: 8px 15px; border-radius: 20px; display: inline-block; font-weight: 600;">
                        رقم الحجز: '.$booking_data['booking_reference'].'
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                            <h6 style="color: #ff9500; margin-bottom: 15px; font-weight: 600;">معلومات الرحلة</h6>
                            <div style="margin-bottom: 8px;">
                                <strong>من:</strong> '.$booking_data['trip']['from_city'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>محطة السفر:</strong> '.$booking_data['trip']['from_station'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>إلى:</strong> '.$booking_data['trip']['to_city'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>محطة الوصول:</strong> '.$booking_data['trip']['to_station'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>تاريخ السفر:</strong> '.date('d/m/Y', strtotime($booking_data['travel_date'])).'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>وقت المغادرة:</strong> '.$booking_data['trip']['departure_time'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>وقت الوصول:</strong> '.$booking_data['trip']['arrival_time'].'
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                            <h6 style="color: #ff9500; margin-bottom: 15px; font-weight: 600;">معلومات المسافر</h6>
                            <div style="margin-bottom: 8px;">
                                <strong>الاسم:</strong> '.$booking_data['passenger_name'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>رقم الهاتف:</strong> '.$booking_data['phone'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>رقم واتساب:</strong> '.$booking_data['whatsapp'].'
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الدفع -->
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 20px;">
                    <h6 style="color: #856404; margin-bottom: 15px; font-weight: 600;">ملخص الدفع</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div style="margin-bottom: 8px;">
                                <strong>عدد المسافرين:</strong> '.$booking_data['seats_count'].'
                            </div>
                            <div style="margin-bottom: 8px;">
                                <strong>سعر المقعد الواحد:</strong> '.$booking_data['trip']['seat_price'].' ريال
                            </div>
                        </div>
                        <div class="col-md-6 text-center">
                            <div style="font-size: 18px; font-weight: bold; color: #ff9500;">
                                المبلغ الإجمالي: '.$booking_data['total_amount'].' ريال
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تعليمات مهمة -->
            <div style="background: #fff3cd; border-left: 4px solid #ffc107; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <h6 style="color: #856404; margin-bottom: 15px; font-weight: 600;">تعليمات مهمة</h6>
                <ul style="margin: 0; padding-right: 20px; color: #856404;">
                    <li style="margin-bottom: 8px;">يرجى الوصول إلى المحطة قبل موعد المغادرة بـ 30 دقيقة على الأقل</li>
                    <li style="margin-bottom: 8px;">تأكد من إحضار هويتك الشخصية أو جواز السفر</li>
                    <li style="margin-bottom: 8px;">احتفظ برقم الحجز للمراجعة أو التعديل</li>
                    <li style="margin-bottom: 8px;">في حالة التأخير أو عدم الحضور، لن يتم استرداد المبلغ</li>
                    <li style="margin-bottom: 8px;">يمكنك إلغاء الحجز قبل 24 ساعة من موعد السفر</li>
                </ul>
            </div>

            <!-- أزرار العمليات -->
            <div style="border-top: 1px solid #e9ecef; padding-top: 20px; text-align: center;">
                <div class="row">
                    <div class="col-6">
                        <a href="download-ticket.php?ref='.$booking_data['booking_reference'].'" style="background: white; border: 1px solid #ff9500; color: #ff9500; border-radius: 6px; padding: 10px 20px; width: 100%; font-weight: 600; text-decoration: none; display: inline-block; text-align: center;">
                            تحميل التذكرة
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="bus-booking.php" style="background: #ff9500; border: none; color: white; border-radius: 6px; padding: 10px 20px; width: 100%; font-weight: 600; text-decoration: none; display: inline-block;">
                            حجز رحلة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تصميم بسيط وواضح للموبايل */
@media print {
    div[style*="border-top"] {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .container {
        margin: 10px !important;
        padding: 0 10px !important;
    }

    .success-card, .booking-details, div[style*="background: #fff3cd"] {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .success-card h2 {
        font-size: 20px !important;
    }

    .booking-details h4 {
        font-size: 18px !important;
    }
}
</style>';

include('footer.php');
?>
