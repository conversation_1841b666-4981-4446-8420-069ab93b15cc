<?php
$Title_page = 'تأكيد الحجز';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود بيانات الحجز
if (!isset($_SESSION['booking_success']) && !isset($_GET['ref'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = null;

// إذا كانت البيانات في الجلسة
if (isset($_SESSION['booking_success'])) {
    $booking_data = $_SESSION['booking_success'];
    unset($_SESSION['booking_success']); // حذف البيانات من الجلسة
}
// أو جلب البيانات من قاعدة البيانات باستخدام الرقم المرجعي
elseif (isset($_GET['ref'])) {
    $booking_ref = $_GET['ref'];

    $stmt = $db->prepare("SELECT b.*, t.* FROM bus_bookings b
                         JOIN bus_trips t ON b.trip_id = t.id
                         WHERE b.booking_reference = ?");
    $stmt->execute([$booking_ref]);
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($booking) {
        $booking_data = [
            'booking_id' => $booking['id'],
            'booking_reference' => $booking['booking_reference'],
            'trip' => $booking,
            'travel_date' => $booking['travel_date'],
            'seats_count' => $booking['seats_count'],
            'total_amount' => $booking['total_amount'],
            'passenger_name' => $booking['passenger_name'],
            'phone' => $booking['passenger_phone'],
            'whatsapp' => $booking['passenger_whatsapp']
        ];
    }
}

if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

echo '<div class="container-fluid p-0">
    <div class="booking-confirmation-hero">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="confirmation-container">
                        <div class="confirmation-card">
                            <!-- رسالة النجاح -->
                            <div class="success-header text-center mb-4">
                                <div class="success-icon mb-3">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                                <h2 class="success-title">تم تأكيد حجزك بنجاح!</h2>
                                <p class="success-subtitle">شكراً لك، تم حجز رحلتك بنجاح</p>
                            </div>
                            
                            <!-- تفاصيل الحجز -->
                            <div class="booking-details">
                                <div class="booking-header mb-4">
                                    <h4>تفاصيل الحجز</h4>
                                    <div class="booking-number">
                                        <strong>رقم الحجز: </strong>
                                        <span class="number">'.$booking['booking_number'].'</span>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="detail-section">
                                            <h6>معلومات الرحلة</h6>
                                            <div class="detail-item">
                                                <strong>من:</strong> '.$booking['from_city'].' - '.$booking['from_station'].'
                                            </div>
                                            <div class="detail-item">
                                                <strong>إلى:</strong> '.$booking['to_city'].' - '.$booking['to_station'].'
                                            </div>
                                            <div class="detail-item">
                                                <strong>تاريخ السفر:</strong> '.date('d/m/Y', strtotime($booking['travel_date'])).'
                                            </div>
                                            <div class="detail-item">
                                                <strong>وقت المغادرة:</strong> '.$booking['departure_time'].'
                                            </div>
                                            <div class="detail-item">
                                                <strong>وقت الوصول:</strong> '.$booking['arrival_time'].'
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="detail-section">
                                            <h6>معلومات المسافر</h6>
                                            <div class="detail-item">
                                                <strong>الاسم:</strong> '.(isset($booking_data) ? $booking_data['passenger_name'] : $booking['passenger_name']).'
                                            </div>
                                            <div class="detail-item">
                                                <strong>رقم الهاتف:</strong> '.(isset($booking_data) ? $booking_data['phone'] : $booking['passenger_phone']).'
                                            </div>
                                            <div class="detail-item">
                                                <strong>رقم واتساب:</strong> '.(isset($booking_data) ? $booking_data['whatsapp'] : (isset($booking['passenger_whatsapp']) ? $booking['passenger_whatsapp'] : $booking['passenger_phone'])).'
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="payment-summary mt-4">
                                    <h6>ملخص الدفع</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <strong>عدد المسافرين:</strong> '.$booking['seats_count'].'
                                            </div>
                                            <div class="detail-item">
                                                <strong>سعر المقعد الواحد:</strong> '.$booking['seat_price'].' ريال
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="total-amount">
                                                <strong>المبلغ الإجمالي: </strong>
                                                <span class="amount">'.$booking['total_amount'].' ريال</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- تعليمات مهمة -->
                            <div class="important-notes mt-4">
                                <h6><i class="fa fa-exclamation-triangle text-warning"></i> تعليمات مهمة</h6>
                                <ul class="notes-list">
                                    <li>يرجى الوصول إلى المحطة قبل موعد المغادرة بـ 30 دقيقة على الأقل</li>
                                    <li>تأكد من إحضار هويتك الشخصية أو جواز السفر</li>
                                    <li>احتفظ برقم الحجز للمراجعة أو التعديل</li>
                                    <li>في حالة التأخير أو عدم الحضور، لن يتم استرداد المبلغ</li>
                                    <li>يمكنك إلغاء الحجز قبل 24 ساعة من موعد السفر</li>
                                </ul>
                            </div>
                            
                            <!-- أزرار العمليات -->
                            <div class="action-buttons mt-4">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button class="btn btn-outline-primary me-md-2" onclick="window.print()">
                                        <i class="fa fa-print"></i> طباعة التذكرة
                                    </button>
                                    <a href="bus-booking.php" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> حجز رحلة جديدة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.booking-confirmation-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.confirmation-container {
    padding: 2rem;
}

.confirmation-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.success-icon {
    font-size: 4rem;
    color: #28a745;
}

.success-title {
    color: #28a745;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.success-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

.booking-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.booking-number .number {
    background: #ff8c00;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1rem;
}

.detail-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    height: 100%;
}

.detail-section h6 {
    color: #ff8c00;
    font-weight: bold;
    margin-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.detail-item {
    margin-bottom: 0.75rem;
    padding: 0.25rem 0;
}

.payment-summary {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
}

.total-amount {
    text-align: center;
    font-size: 1.2rem;
}

.total-amount .amount {
    color: #ff8c00;
    font-weight: bold;
    font-size: 1.4rem;
}

.important-notes {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 5px;
    padding: 1.5rem;
}

.important-notes h6 {
    color: #856404;
    margin-bottom: 1rem;
}

.notes-list {
    margin-bottom: 0;
    padding-right: 1rem;
}

.notes-list li {
    margin-bottom: 0.5rem;
    color: #856404;
}

.action-buttons {
    border-top: 2px solid #f8f9fa;
    padding-top: 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-outline-primary {
    border-color: #ff8c00;
    color: #ff8c00;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-outline-primary:hover {
    background-color: #ff8c00;
    border-color: #ff8c00;
}

@media print {
    .action-buttons {
        display: none !important;
    }
    
    .confirmation-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

@media (max-width: 768px) {
    .confirmation-container {
        padding: 1rem;
    }
    
    .confirmation-card {
        padding: 1.5rem;
    }
    
    .success-icon {
        font-size: 3rem;
    }
}
</style>';

include('footer.php');
?>
